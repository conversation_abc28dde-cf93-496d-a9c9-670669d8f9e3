import { prismaPG } from '@config/db';
import type { PostReaction, Prisma } from '@prisma/postgres';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { ReactionFetchManyI, ReactionPostIdI, ReactionUpsertI } from '@schemas/feed/reaction';
import { ReactionDataClientI, ReactionExternalClientI } from '@interfaces/feed/reaction';
import AppError from '@classes/AppError';
import { ProfileExternalI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';
import { PostForNotificationI } from '@interfaces/feed/post';

export const ReactionModule = {
  upsert: async (
    state: FastifyStateI,
    { postId, reactionType }: ReactionUpsertI,
  ): Promise<Pick<PostReaction, 'postId'>> => {
    const selfProfileId = state.profileId;

    const existingReactionResult = await prismaPG.postReaction.findUnique({
      select: {
        reactionType: true,
      },
      where: {
        profileId_postId: {
          postId,
          profileId: selfProfileId,
        },
      },
    });

    if (existingReactionResult?.reactionType === reactionType) {
      throw new AppError('PRCT007');
    }

    const postResultTemp = await prismaPG.$queryRaw<PostForNotificationI[]>`
           SELECT
             p.id,
             LEFT(p.caption, 20) AS "caption",
             (
               SELECT pm."fileUrl"
               FROM "feed"."PostMedia" pm
               WHERE pm."postId" = p.id
               ORDER BY pm."createdAt" ASC
               LIMIT 1
             ) AS "image"
           FROM
             "feed"."Post" p
           WHERE
             p.id = ${postId}::uuid
         `;

    if (!postResultTemp?.length) {
      throw new AppError('POST006');
    }
    const postResult = postResultTemp[0];

    const [reactionResult, selfProfileResult] = await Promise.all([
      prismaPG.postReaction.upsert({
        select: {
          postId: true,
          Post: {
            select: {
              profileId: true,
            },
          },
        },
        create: {
          reactionType,
          postId,
          profileId: selfProfileId,
        },
        update: { reactionType },
        where: {
          profileId_postId: {
            postId,
            profileId: selfProfileId,
          },
        },
      }),
      prismaPG.profile.findUnique({
        select: { name: true },
        where: { id: selfProfileId },
      }),
    ]);

    if (existingReactionResult?.reactionType !== reactionType) {
      await prismaPG.post.update({
        data: {
          reactionsCount: {
            increment: 1,
          },
        },
        where: {
          id: postId,
        },
      });

      if (selfProfileId !== reactionResult.Post.profileId) {
        await CommunicationModule.NotificationModule.createOne({
          actorProfileId: selfProfileId,
          actorProfileName: selfProfileResult?.name ?? 'Unknown',
          postId,
          postText: postResult?.caption?.length > 17 ? `${postResult.caption.slice(0, 17)}...` : postResult.caption,
          profileId: reactionResult.Post.profileId,
          topic: 'communication_topic',
          type: 'LIKE',
        });
      }
    }

    return reactionResult;
  },
  fetchMany: async (
    state: FastifyStateI,
    { postId, page, pageSize }: ReactionFetchManyI,
  ): Promise<{ reactions: ReactionExternalClientI[]; totalCount: number }> => {
    const selfProfileId = state.profileId;
    const reactionResult: ReactionExternalClientI[] = [];
    const filters: Prisma.PostReactionWhereInput = {
      postId,
      NOT: {
        Profile: {
          status: {
            equals: 'ACTIVE',
          },
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };
    const [totalCount, reactionResultTemp]: [number, ReactionDataClientI[]] = await Promise.all([
      prismaPG.postReaction.count({
        where: filters,
      }),
      prismaPG.postReaction.findMany({
        select: {
          postId: true,
          reactionType: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
        where: filters,
        orderBy: {
          createdAt: 'desc',
        },
        skip: page * pageSize,
        take: pageSize,
      }),
    ]);

    if (reactionResultTemp?.length) {
      reactionResult.push(
        ...reactionResultTemp.map((item) => ({
          ...item,
          Profile: {
            id: item.Profile.id,
            name: item.Profile.name,
            avatar: item.Profile.avatar,
            designation: item.Profile?.designationAlternativeId
              ? {
                  id: item.Profile.designationAlternativeId,
                  name: item.Profile.designationText,
                  dataType: 'master',
                }
              : item.Profile?.designationRawDataId
                ? {
                    id: item.Profile.designationAlternativeId,
                    name: item.Profile.designationText,
                    dataType: 'raw',
                  }
                : null,
            entity: item.Profile?.entityId
              ? {
                  id: item.Profile.entityId,
                  name: item.Profile.entityText,
                  dataType: 'master',
                }
              : item.Profile?.entityRawDataId
                ? {
                    id: item.Profile.entityRawDataId,
                    name: item.Profile.entityText,
                    dataType: 'raw',
                  }
                : null,
          } as ProfileExternalI,
        })),
      );
    }

    return { reactions: reactionResult, totalCount };
  },
  deleteOne: async (state: FastifyStateI, { postId }: ReactionPostIdI): Promise<void> => {
    const existingReactionResult = await prismaPG.postReaction.findUnique({
      select: {
        postId: true,
      },
      where: {
        profileId_postId: {
          postId,
          profileId: state.profileId,
        },
      },
    });
    if (!existingReactionResult) {
      throw new AppError('PRCT001');
    }
    const [_reactionResult, postResult] = await Promise.all([
      prismaPG.postReaction.delete({
        select: {
          postId: true,
        },
        where: {
          profileId_postId: {
            postId,
            profileId: state.profileId,
          },
        },
      }),
      prismaPG.post.findUnique({
        select: { reactionsCount: true },
        where: { id: postId },
      }),
    ]);
    if (postResult?.reactionsCount > 0) {
      await prismaPG.post.update({
        data: {
          reactionsCount: {
            decrement: 1,
          },
        },
        where: {
          id: postId,
        },
      });
    }
    return;
  },
};
