import AppError from '@classes/AppError';
import { PAGINATION } from '@consts/common/pagination';
import { Prisma } from '@prisma/postgres';
import { PaginationI } from '@schemas/common/common';
import { sortArrayByString } from '@utils/data/array';
import { prismaPG } from '@config/db';
import { CityClientI, CityNestedClientI } from '@interfaces/master/city';
import { DBDataTypeI } from '@consts/common/data';
import { ObjUnknownI } from '@interfaces/common/data';
import { CityFetchOneParamsI, CityFetchForClientParamsI } from '@schemas/master/city';
import { CountryModule } from './country';
import { isFilled } from '@utils/data/object';
import { PlaceSourceI } from '@consts/master/place';

export const CityModule = {
  fetchById: async (id: string, isThrowingError: boolean = true): Promise<CityNestedClientI> => {
    const cityRawDataResult = await prismaPG.cityRawData.findFirst({
      where: {
        id,
      },
      select: {
        id: true,
        name: true,
      },
    });
    if (cityRawDataResult) {
      return {
        ...cityRawDataResult,
        dataType: 'raw',
      } as CityNestedClientI;
    }
    const cityResult = await prismaPG.city.findFirst({
      where: { id },
      select: {
        id: true,
        name: true,
      },
    });

    if (cityResult) {
      return {
        ...(cityResult as ObjUnknownI),
        dataType: 'master',
      } as CityNestedClientI;
    }

    if (isThrowingError) {
      throw new AppError('CITY001');
    }
  },
  fetchByOne: async (filtersP: CityFetchOneParamsI, _isThrowingError: boolean = true): Promise<CityNestedClientI> => {
    if (filtersP.city.dataType === 'raw') {
      const filters: Prisma.CityRawDataWhereInput = {
        id: filtersP.city.id,
      };
      if (filtersP.countryIso2?.length) {
        filters.countryIso2 = filtersP.countryIso2;
      }
      const cityRawDataResult = await prismaPG.cityRawData.findFirst({
        where: filters,
        select: {
          id: true,
          name: true,
        },
      });
      if (cityRawDataResult) {
        return {
          ...cityRawDataResult,
          dataType: 'raw',
        } as CityNestedClientI;
      }
    } else if (filtersP.city.dataType === 'master') {
      const filters: Prisma.CityWhereInput = {
        id: filtersP.city.id,
      };
      if (filtersP.countryIso2?.length) {
        filters.countryIso2 = filtersP.countryIso2;
      }
      const cityResult = await prismaPG.city.findFirst({
        where: filters,
        select: {
          id: true,
          name: true,
        },
      });

      if (cityResult) {
        return {
          ...(cityResult as ObjUnknownI),
          dataType: 'master',
        } as CityNestedClientI;
      }
    }

    if (_isThrowingError) {
      throw new AppError('CITY001');
    }
  },
  fetchForClient: async (
    filtersP: CityFetchForClientParamsI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: CityNestedClientI[]; total: number }> => {
    let citysResult: CityNestedClientI[] = [];
    const cityFilters: Prisma.CityWhereInput = {};
    const cityRawDataFilters: Prisma.CityRawDataWhereInput = {};
    if (filtersP.search?.length) {
      cityFilters.name = {
        startsWith: filtersP.search.trim().toLowerCase(),
        mode: 'insensitive',
      };
      cityRawDataFilters.name = {
        startsWith: filtersP.search.trim().toLowerCase(),
        mode: 'insensitive',
      };
    }
    if (filtersP.countryIso2?.length) {
      cityFilters.countryIso2 = filtersP.countryIso2;
      cityRawDataFilters.countryIso2 = filtersP.countryIso2;
    }
    const [cityResultTemp, cityRawDataTemp, cityResultTotal, cityRawDataResultTotal] = await Promise.all([
      prismaPG.city.findMany({
        where: cityFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
      prismaPG.cityRawData.findMany({
        where: cityRawDataFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
      prismaPG.city.count({
        where: cityFilters,
      }),
      prismaPG.cityRawData.count({
        where: cityRawDataFilters,
      }),
    ]);

    if (cityResultTemp?.length) {
      citysResult.push(
        ...cityResultTemp.map(
          (city) =>
            ({
              ...city,
              dataType: 'master',
            }) as CityNestedClientI,
        ),
      );
    }

    if (cityRawDataTemp?.length) {
      citysResult.push(
        ...cityResultTemp.map(
          (city) =>
            ({
              ...city,
              dataType: 'raw',
            }) as CityNestedClientI,
        ),
      );
    }
    if (citysResult?.length) {
      citysResult = sortArrayByString(citysResult, 'name', 'asc');
    }
    return { data: citysResult, total: cityResultTotal + cityRawDataResultTotal };
  },
  fetchsert: async (
    params: Prisma.CityRawDataUncheckedUpdateInput,
    filters: Prisma.CityRawDataWhereInput,
    dataType?: DBDataTypeI,
  ): Promise<CityClientI> => {
    const select: Prisma.CitySelect = {
      id: true,
      geoNameId: true,
      name: true,
      countryIso2: true,
      latitude: true,
      longitude: true,
      mapboxId: true,
    };

    let [cityResult, cityRawDataResult] = await Promise.all([
      dataType === 'raw'
        ? null
        : prismaPG.city.findFirst({
            where: filters as Prisma.CityWhereInput,
            select,
          }),
      dataType === 'master'
        ? null
        : prismaPG.cityRawData.findFirst({
            where: filters,
            select: { ...select, source: true },
          }),
    ]);
    if (cityResult) {
      const input: Prisma.CityUncheckedUpdateInput = {};

      if (!cityResult?.geoNameId) {
        input.geoNameId = cityResult.geoNameId;
      }
      if (!cityResult?.latitude) {
        input.latitude = cityResult.latitude;
      }
      if (!cityResult?.longitude) {
        input.longitude = cityResult.longitude;
      }
      if (!cityResult?.mapboxId) {
        input.mapboxId = cityResult.mapboxId;
      }
      if (isFilled(input)) {
        cityResult = await prismaPG.city.update({
          data: input,
          select,
          where: { id: cityResult.id },
        });
      }
    } else if (cityRawDataResult) {
      const input: Prisma.CityRawDataUncheckedUpdateInput = {};
      if (!cityRawDataResult?.geoNameId) {
        input.geoNameId = cityRawDataResult.geoNameId;
      }
      if (!cityRawDataResult?.latitude) {
        input.latitude = cityRawDataResult.latitude;
      }
      if (!cityRawDataResult?.longitude) {
        input.longitude = cityRawDataResult.longitude;
      }
      if (!cityRawDataResult?.mapboxId) {
        input.mapboxId = cityRawDataResult.mapboxId;
      }
      if (isFilled(input)) {
        cityRawDataResult = await prismaPG.cityRawData.update({
          data: input,
          select: { ...select, source: true },
          where: { id: cityRawDataResult.id },
        });
      }
    }
    if (cityResult) {
      return { ...cityResult, dataType: 'master' as DBDataTypeI } as CityNestedClientI;
    } else if (cityRawDataResult) {
      return { ...cityRawDataResult, dataType: 'raw' as DBDataTypeI } as CityNestedClientI;
    }

    const [_countryResult] = await Promise.all([
      params?.countryIso2 ? CountryModule.fetchOne({ iso2: String(params.countryIso2) }) : null,
    ]);

    const cityRawDataInput: Prisma.CityRawDataUncheckedCreateInput = {
      name: String(params.name),
      countryIso2: String(params.countryIso2),
    };
    if (params?.geoNameId) {
      cityRawDataInput.geoNameId = String(params.geoNameId);
    }
    if (params?.latitude) {
      cityRawDataInput.latitude = Number(params.latitude);
    }
    if (params?.longitude) {
      cityRawDataInput.longitude = Number(params.longitude);
    }
    if (params?.mapboxId) {
      cityRawDataInput.mapboxId = String(params.mapboxId);
    }
    if (params?.source) {
      cityRawDataInput.source = params.source as PlaceSourceI;
    }
    const result = await prismaPG.cityRawData.create({ data: cityRawDataInput });
    if (!result?.id) {
      throw new AppError('CITY002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as CityNestedClientI;
  },
  transform: (data: {
    City: {
      id: string;
      name: string;
    };
    CityRawData: {
      id: string;
      name: string;
    };
  }): CityNestedClientI => ({
    id: data.City?.id ? data.City.id : data.CityRawData?.id ? data.CityRawData.id : null,
    name: data.City?.name ? data.City.name : data.CityRawData?.name ? data.CityRawData.name : null,
    dataType: data.City?.id ? 'master' : 'raw',
  }),
};
