import { prismaPG } from '@config/db';
import type {
  FetchByImoDetailedShipClientI,
  FetchPrePopulateResultI,
  FetchShipClientI,
  ShipFetchByImoSelectedI,
  ShipNestedClientI,
} from '@interfaces/ship/ship';
import Master from '@modules/master';
import { Prisma } from '@prisma/postgres';
import type {
  ShipFetchForClientI,
  ShipFetchsertParamsI,
  ShipImoClientI,
  ShipSearchI,
  ShipVisitorFetchForClientParamsI,
} from '@schemas/ship/ship';
import { MainVesselTypeModule } from './mainVesselType';
import type { PaginationI } from '@schemas/common/common';
import { PAGINATION } from '@consts/common/pagination';
import AppError from '@classes/AppError';
import type {
  ShipSearchByNameExternalResultI,
  ShipSearchDataItemI,
  ShipSearchQueryDataItemI,
} from '@interfaces/network/searchShip';
import { errorHandler } from '@utils/errors/handler';
import { IsShipImoR } from '@consts/common/regex/regex';
import type { PostgresTxnI, TotalI } from '@interfaces/common/db';
import type { PeopleI, PeopleRawI } from '@interfaces/user/profile';
import { DesignationNestedClientI } from '@interfaces/company/designation';
import { EntityNestedExternalI } from '@interfaces/company/entity';
import { TotalDataI } from '@interfaces/common/data';
import { FastifyStateI } from '@interfaces/common/declaration';
import { isFilled } from '@utils/data/object';
import { SubVesselTypeModule } from './subVesselType';

export const CoreShipModule = {
  fetchByImo: async (filters: ShipImoClientI, txn: PostgresTxnI = prismaPG): Promise<ShipNestedClientI> => {
    if (filters.dataType === 'master') {
      const shipResult = await txn.ship.findFirst({
        where: { imo: filters.imo },
        select: {
          imo: true,
          name: true,
        },
      });
      return {
        ...shipResult,
        dataType: 'master',
      };
    } else if (filters.dataType === 'raw') {
      const shipRawDataResult = await txn.shipRawData.findFirst({
        where: { imo: filters.imo },
        select: {
          imo: true,
          name: true,
        },
      });
      return {
        ...shipRawDataResult,
        dataType: 'raw',
      };
    }
    throw new AppError('SHIP001');
  },
  fetchByImoSelected: async (
    { imo, dataType }: ShipImoClientI,
    _isThrowingError: boolean = true,
    select: Prisma.ShipRawDataSelect = {
      imo: true,
      name: true,
    },
  ): Promise<ShipFetchByImoSelectedI> => {
    if (dataType === 'master') {
      const shipResult = await prismaPG.ship.findFirst({
        where: { imo },
        select,
      });

      if (!shipResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
      }

      return { ...shipResult, dataType: 'master' };
    } else if (dataType === 'raw') {
      const shipRawDataResult = await prismaPG.shipRawData.findFirst({
        where: { imo },
        select,
      });

      if (!shipRawDataResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
        return null;
      }
      return { ...shipRawDataResult, dataType: 'raw' };
    }

    if (_isThrowingError) {
      throw new AppError('SHIP001');
    }
  },
  fetchByImoDetailed: async (
    { imo, dataType }: ShipImoClientI,
    _isThrowingError: boolean = true,
  ): Promise<FetchByImoDetailedShipClientI> => {
    if (dataType === 'master') {
      const shipResult = await prismaPG.ship.findFirst({
        where: { imo },
        select: {
          imo: true,
          mmsi: true,
          callSign: true,
          name: true,
          yearBuilt: true,
          imageUrl: true,
          Country: {
            select: {
              name: true,
            },
          },
          MainVesselType: {
            select: { name: true },
          },
          SubVesselType: {
            select: { name: true },
          },
          ShipName: {
            select: {
              name: true,
              fromDate: true,
              toDate: true,
            },
            orderBy: [
              {
                toDate: {
                  sort: 'desc',
                  nulls: 'first',
                },
              },
              {
                fromDate: 'desc',
              },
              {
                createdAt: 'desc',
              },
            ],
          },
        },
      });

      if (!shipResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
      }

      return {
        imo: shipResult.imo,
        mmsi: shipResult.mmsi,
        callSign: shipResult.callSign,
        name: shipResult.name,
        imageUrl: shipResult.imageUrl,
        yearBuilt: shipResult.yearBuilt,
        country: shipResult?.Country,
        mainVesselType: shipResult?.MainVesselType,
        subVesselType: shipResult?.SubVesselType,
        dataType: 'master',
        ...(shipResult?.name?.length ? { shipNames: shipResult?.ShipName } : {}),
      };
    } else if (dataType === 'raw') {
      const shipRawDataResult = await prismaPG.shipRawData.findFirst({
        where: { imo },
        select: {
          imo: true,
          mmsi: true,
          callSign: true,
          name: true,
          yearBuilt: true,
          Country: {
            select: {
              name: true,
            },
          },
          MainVesselType: {
            select: { name: true },
          },
          SubVesselType: {
            select: { name: true },
          },
          SubVesselTypeRawData: {
            select: { name: true },
          },
        },
      });

      if (!shipRawDataResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
        return null;
      }

      return {
        imo: shipRawDataResult.imo,
        mmsi: shipRawDataResult.mmsi,
        callSign: shipRawDataResult.callSign,
        name: shipRawDataResult.name,
        yearBuilt: shipRawDataResult.yearBuilt,
        country: shipRawDataResult.Country,
        mainVesselType: shipRawDataResult.MainVesselType,
        subVesselType: isFilled(shipRawDataResult.SubVesselType)
          ? shipRawDataResult.SubVesselType
          : shipRawDataResult.SubVesselTypeRawData,
        dataType: 'raw',
      };
    }

    if (_isThrowingError) {
      throw new AppError('SHIP001');
    }
  },
  fetchPrePopulate: async (
    { imo, dataType }: ShipImoClientI,
    _isThrowingError: boolean = true,
  ): Promise<FetchPrePopulateResultI> => {
    if (dataType === 'master') {
      const shipResult = await prismaPG.ship.findFirst({
        where: { imo },
        select: {
          imo: true,
          name: true,
          length: true,
          beam: true,
          gt: true,
          dwt: true,
          ShipName: {
            select: {
              name: true,
              fromDate: true,
              toDate: true,
            },
            orderBy: [
              {
                toDate: {
                  sort: 'desc',
                  nulls: 'first',
                },
              },
              {
                fromDate: 'desc',
              },
              {
                createdAt: 'desc',
              },
            ],
            take: 1,
          },
          SubVesselType: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!shipResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
        return null;
      }

      return {
        imo: shipResult.imo,
        name: shipResult.ShipName?.[0]?.name || shipResult?.name,
        length: shipResult.length,
        beam: shipResult.beam,
        gt: shipResult.gt,
        dwt: shipResult.dwt,
        subVesselType: {
          ...shipResult.SubVesselType,
          dataType: 'master',
        },
        dataType: 'master',
      };
    } else if (dataType === 'raw') {
      const shipRawDataResult = await prismaPG.shipRawData.findFirst({
        where: { imo },
        select: {
          imo: true,
          name: true,
          length: true,
          beam: true,
          gt: true,
          dwt: true,
          SubVesselType: {
            select: {
              id: true,
              name: true,
            },
          },
          SubVesselTypeRawData: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!shipRawDataResult) {
        if (_isThrowingError) throw new AppError('SHIP001');
        return null;
      }

      return {
        imo: shipRawDataResult.imo,
        name: shipRawDataResult?.name,
        length: shipRawDataResult.length,
        beam: shipRawDataResult.beam,
        gt: shipRawDataResult.gt,
        dwt: shipRawDataResult.dwt,
        subVesselType: isFilled(shipRawDataResult.SubVesselType)
          ? {
              id: shipRawDataResult.SubVesselType.id,
              name: shipRawDataResult.SubVesselType.name,
              dataType: 'master',
            }
          : {
              id: shipRawDataResult.SubVesselTypeRawData.id,
              name: shipRawDataResult.SubVesselTypeRawData.name,
              dataType: 'raw',
            },
        dataType: 'raw',
      };
    }

    if (_isThrowingError) {
      throw new AppError('SHIP001');
    }
  },
  fetchForClient: async (
    filtersP: ShipFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<FetchShipClientI[]> => {
    const search = filtersP.search.trim().toLowerCase();
    const result: FetchShipClientI[] = await prismaPG.$queryRaw<FetchShipClientI[]>`
      SELECT * FROM (SELECT s."imo" AS "imo", s."name" as "currentName", nm."name" AS "matchingName", 'master' AS "dataType" FROM "ShipName" nm INNER JOIN "Ship" s ON nm."shipImo" = s."imo"
      WHERE nm."name" ILIKE ${search + '%'}
      OR s."imo" ILIKE ${search + '%'}
      UNION
      SELECT srw."imo" AS "imo", srw."name" AS "currentName", NULL AS "matchingName", 'rawData' AS "dataType" FROM "ShipRawData" srw
      WHERE srw."name" ILIKE ${search + '%'})
      AS combined_results
      LIMIT ${pagination.pageSize} OFFSET ${pagination.page * pagination.pageSize}
    `;
    return result;
  },
  fetchsert: async (params: ShipFetchsertParamsI): Promise<ShipNestedClientI> => {
    const [shipResult, shipRawDataResult] = await Promise.all([
      prismaPG.ship.findFirst({
        where: { imo: params.imo },
        select: {
          imo: true,
          name: true,
        },
      }),
      prismaPG.shipRawData.findFirst({
        where: { imo: params.imo },
        select: {
          imo: true,
          name: true,
        },
      }),
    ]);
    if (shipResult) {
      return { ...shipResult, dataType: 'master' } as ShipNestedClientI;
    } else if (shipRawDataResult) {
      return { ...shipRawDataResult, dataType: 'raw' } as ShipNestedClientI;
    } else {
      const [_countryResult, _mainVesselTypeResult, _subVesselTypeResult] = await Promise.all([
        params?.flagCountryIso2?.length ? Master.CountryModule.fetchOne({ iso2: params.flagCountryIso2 }) : null,
        params?.mainVesselType?.id?.length
          ? MainVesselTypeModule.fetchOne({
              id: params.mainVesselType.id,
              dataType: params.mainVesselType.dataType,
            })
          : null,
        params?.subVesselType?.id?.length
          ? SubVesselTypeModule.fetchById({
              id: params.subVesselType.id,
              dataType: params.subVesselType.dataType,
            })
          : null,
      ]);

      const createInput: Prisma.ShipRawDataUncheckedCreateInput = { imo: params.imo, name: params.name };
      if (params?.mmsi?.length) {
        createInput.mmsi = Number(params.mmsi);
      }
      if (params?.callSign?.length) {
        createInput.callSign = params.callSign;
      }
      if (params?.flagCountryIso2?.length) {
        createInput.flagCountryIso2 = params.flagCountryIso2;
      }
      if (params?.mainVesselType?.id?.length) {
        if (params.mainVesselType.dataType === 'master') {
          createInput.mainVesselTypeId = params.mainVesselType.id;
        } else if (params.mainVesselType.dataType === 'raw') {
          createInput.mainVesselTypeRawDataId = params.mainVesselType.id;
        }
      }
      if (params?.subVesselType?.id?.length) {
        if (params.subVesselType.dataType === 'master') {
          createInput.subVesselTypeId = params.subVesselType.id;
        } else if (params.subVesselType.dataType === 'raw') {
          createInput.subVesselTypeRawDataId = params.subVesselType.id;
        }
      }
      if (params?.yearBuilt?.length) {
        createInput.yearBuilt = parseInt(params.yearBuilt);
      }

      const shipRawDataResult = await prismaPG.shipRawData.create({
        data: createInput,
        select: {
          imo: true,
          name: true,
        },
      });
      return { ...shipRawDataResult, dataType: 'raw' } as ShipNestedClientI;
    }
  },

  search: async ({ page, pageSize, search: searchText }: ShipSearchI): Promise<ShipSearchByNameExternalResultI> => {
    try {
      const isImoSearch = IsShipImoR.test(searchText);
      const [shipsResultTemp, shipsTotalResult] = await Promise.all([
        prismaPG.$queryRaw<ShipSearchQueryDataItemI[]>(
          isImoSearch
            ? Prisma.sql`
                SELECT "imo", "name", "matchedName", "imageUrl", "dataType"
                FROM (
                  SELECT
                    s."imo" AS "imo",
                    s."name" AS "name",
                    s."name" AS "matchedName",
                    s."imageUrl" AS "imageUrl",
                    'master' AS "dataType"
                  FROM "ship"."Ship" s
                  WHERE s."imo" ILIKE ${searchText?.toLowerCase() + '%'}

                  UNION ALL

                  SELECT
                    sr."imo" AS "imo",
                    sr."name" AS "name",
                    sr."name" AS "matchedName",
                    NULL AS "imageUrl",
                    'raw' AS "dataType"
                  FROM "rawData"."ShipRawData" sr
                  WHERE sr."imo" ILIKE ${searchText?.toLowerCase() + '%'}
                )
                ORDER BY "imo" ASC
                OFFSET ${page * pageSize}
                LIMIT ${pageSize}
              `
            : Prisma.sql`
                SELECT "imo", "name", "matchedName", "imageUrl", "dataType"
                FROM (
                  SELECT
                    s."imo" AS "imo",
                    s."name" AS "name",
                    sn."name" AS "matchedName",
                    s."imageUrl" AS "imageUrl",
                    'master' AS "dataType"
                  FROM "ship"."ShipName" sn
                  INNER JOIN "ship"."Ship" s
                  ON sn."shipImo" = s."imo"
                  WHERE sn."name" ILIKE ${searchText?.toLowerCase() + '%'}

                  UNION ALL

                  SELECT
                    sr."imo" AS "imo",
                    sr."name" AS "name",
                    sr."name" AS "matchedName",
                    NULL AS "imageUrl",
                    'raw' AS "dataType"
                  FROM "rawData"."ShipRawData" sr
                  WHERE sr."name" ILIKE ${searchText?.toLowerCase() + '%'}
                )
                ORDER BY "matchedName" ASC
                OFFSET ${page * pageSize}
                LIMIT ${pageSize}
              `,
        ),
        prismaPG.$queryRaw<{ total: number }[]>(
          isImoSearch
            ? Prisma.sql`
                SELECT SUM(total) AS total
                FROM (
                  SELECT COUNT(1) AS total
                  FROM "ship"."Ship" s
                  WHERE s."imo" ILIKE ${searchText?.toLowerCase() + '%'}
                  UNION ALL
                  SELECT COUNT(1) AS total
                  FROM "rawData"."ShipRawData" sr
                  WHERE sr."imo" ILIKE ${searchText?.toLowerCase() + '%'}
                )
              `
            : Prisma.sql`
                SELECT SUM(total) AS total
                FROM (
                  SELECT COUNT(1) AS total
                  FROM "ship"."ShipName" sn
                  INNER JOIN "ship"."Ship" s
                  ON sn."shipImo" = s."imo"
                  WHERE sn."name" ILIKE ${searchText?.toLowerCase() + '%'}
                  UNION ALL
                  SELECT COUNT(1) AS total
                  FROM "rawData"."ShipRawData" sr
                  WHERE sr."name" ILIKE ${searchText?.toLowerCase() + '%'}
                )
              `,
        ),
      ]);

      const ships: ShipSearchDataItemI[] =
        shipsResultTemp?.map((item) => ({
          imo: item.imo,
          name: item.name,
          dataType: item.dataType,
          imageUrl: item.imageUrl,
          matchedName: item.matchedName,
        })) ?? [];

      return {
        data: ships,
        total: Number(shipsTotalResult[0]?.total || 0),
      };
    } catch (error) {
      errorHandler(error, { GENERIC: 'SHIP001' });
    }
  },
  fetchPopularShips: async (_state: FastifyStateI): Promise<ShipSearchByNameExternalResultI> => {
    try {
      const [topShipsRaw, totalCountRaw] = await Promise.all([
        prismaPG.$queryRaw<ShipSearchQueryDataItemI[]>(Prisma.sql`
        SELECT
          s."imo" AS "imo",
          sr."imo" AS "rawImo",
          s."name" AS "name",
          sr."name" AS "rawName",
          COALESCE(s."name", sr."name") AS "matchedName",
          COALESCE(s."imageUrl", NULL) AS "imageUrl",
          CASE
            WHEN s."imo" IS NOT NULL THEN 'master'
            ELSE 'raw'
          END AS "dataType"
        FROM "career"."ExperienceShip" es
        LEFT JOIN "ship"."Ship" s ON es."shipImo" = s."imo"
        LEFT JOIN "rawData"."ShipRawData" sr ON es."shipRawDataImo" = sr."imo"
        INNER JOIN "user"."Profile" u ON u."id" = es."profileId" AND u."status" = 'ACTIVE'
        GROUP BY s."imo", sr."imo", s."name", sr."name", s."imageUrl"
        ORDER BY COUNT(DISTINCT es."profileId") DESC
        LIMIT 5
      `),

        prismaPG.$queryRaw<{ total: number }[]>(Prisma.sql`
        SELECT COUNT(*) AS total
        FROM (
          SELECT
            COALESCE(es."shipImo", es."shipRawDataImo") AS "imo"
          FROM "career"."ExperienceShip" es
          INNER JOIN "user"."Profile" u ON u."id" = es."profileId" AND u."status" = 'ACTIVE'
          GROUP BY "imo"
        ) AS unique_ships
      `),
      ]);

      const data: ShipSearchDataItemI[] = topShipsRaw.map((item) => ({
        imo: item.imo ?? item.rawImo,
        name: item.name ?? item.rawName,
        dataType: item.dataType,
        matchedName: item.matchedName,
        imageUrl: item.imageUrl,
      }));

      return {
        total: Number(totalCountRaw?.[0]?.total || 0),
        data,
      };
    } catch (error) {
      errorHandler(error, { GENERIC: 'SHIP001' });
    }
  },

  fetchVisitors: async (
    state: FastifyStateI,
    { imo, dataType, page, pageSize }: ShipVisitorFetchForClientParamsI,
  ): Promise<TotalDataI<PeopleI>> => {
    const selfProfileId = state.profileId;

    const filters: Prisma.ExperienceShipWhereInput = {};

    if (dataType === 'raw') {
      filters.shipImo = imo;
    } else {
      filters.shipRawDataImo = imo;
    }

    const [visitorCountResult, visitorResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
           SELECT
           COUNT(DISTINCT v."profileId") AS "total"
           FROM "career"."ExperienceShip" v
           INNER JOIN "user"."Profile" u
            ON u."id" = v."profileId"
            AND u."status" = 'ACTIVE'
           WHERE
             ${dataType === 'raw' ? Prisma.sql`v."shipRawDataImo" = ${imo}` : Prisma.sql`v."shipImo" = ${imo}`}
             AND NOT EXISTS (
               SELECT 1
               FROM "network"."BlockedProfile" b
               WHERE (
                 (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
                 OR
                 (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
               )
               LIMIT 1
             )
         `,
      prismaPG.$queryRaw<PeopleRawI[]>`
          SELECT
            DISTINCT ON (u."id")
            u."id",
            u."name",
            u."avatar",
            u."designationText",
            u."designationAlternativeId",
            u."designationRawDataId",
            u."entityText",
            u."entityId",
            u."entityRawDataId",
            CASE
              WHEN u."id" = ${selfProfileId}::uuid THEN false
              ELSE EXISTS (
                SELECT 1
                FROM "network"."Connection" c
                WHERE c."profileId" = ${selfProfileId}::uuid
                  AND c."connectedId" = u."id"
                LIMIT 1
              )
            END AS "isConnected",
            CASE
              WHEN u."id" = ${selfProfileId}::uuid THEN null
              ELSE (
                SELECT r."status"
                FROM "network"."Request" r
                WHERE (
                  (r."senderProfileId" = ${selfProfileId}::uuid AND r."receiverProfileId" = u."id")
                  OR
                  (r."senderProfileId" = u."id" AND r."receiverProfileId" = ${selfProfileId}::uuid)
                )
                ORDER BY r."updatedAt" DESC
                LIMIT 1
              )
            END AS "requestStatus",
            CASE
              WHEN u."id" = ${selfProfileId}::uuid THEN null
              ELSE (
                SELECT
                  CASE
                    WHEN r."senderProfileId" = ${selfProfileId}::uuid THEN 'SENT'
                    WHEN r."receiverProfileId" = ${selfProfileId}::uuid THEN 'RECEIVED'
                    ELSE null
                  END
                FROM "network"."Request" r
                WHERE (
                  (r."senderProfileId" = ${selfProfileId}::uuid AND r."receiverProfileId" = u."id")
                  OR
                  (r."senderProfileId" = u."id" AND r."receiverProfileId" = ${selfProfileId}::uuid)
                )
                ORDER BY r."updatedAt" DESC
                LIMIT 1
              )
            END AS "requestDirection"
          FROM "career"."ExperienceShip" v
          INNER JOIN "user"."Profile" u
            ON u."id" = v."profileId"
            AND u."status" = 'ACTIVE'
          WHERE
            ${dataType === 'raw' ? Prisma.sql`v."shipRawDataImo" = ${imo}` : Prisma.sql`v."shipImo" = ${imo}`}
            AND NOT EXISTS (
              SELECT 1
              FROM "network"."BlockedProfile" b
              WHERE (
                (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
                OR
                (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
              )
              LIMIT 1
            )
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
        `,
    ]);

    const portVisitorFetchForClientResult: PeopleI[] = [];

    if (visitorResultTemp?.length) {
      portVisitorFetchForClientResult.push(
        ...visitorResultTemp.map((visitor) => ({
          id: visitor.id,
          name: visitor.name,
          avatar: visitor.avatar,
          designation: (visitor.designationAlternativeId
            ? { id: visitor.designationAlternativeId, name: visitor.designationText, dataType: 'master' }
            : {
                id: visitor.designationRawDataId,
                name: visitor.designationText,
                dataType: 'raw',
              }) as DesignationNestedClientI,
          entity: (visitor.entityId
            ? { id: visitor.entityId, name: visitor.entityText, dataType: 'master' }
            : { id: visitor.entityRawDataId, name: visitor.entityText, dataType: 'raw' }) as EntityNestedExternalI,
          isConnected: visitor.id === selfProfileId ? null : visitor.isConnected,
          requestStatus: visitor.id === selfProfileId ? null : visitor.requestStatus,
        })),
      );
    }

    return { total: Number(visitorCountResult?.[0]?.total), data: portVisitorFetchForClientResult };
  },
};
