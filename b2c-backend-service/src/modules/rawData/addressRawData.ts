import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { Prisma } from '@prisma/postgres';
import { AddressRawDataParams } from '@schemas/rawData/addressRawData';
import Master from '@modules/master';
import { isFilled, pick } from '@utils/data/object';
import type { AddressRawDataResult } from '@interfaces/rawData/addressRawData';
import { CityClientI } from '@interfaces/master/city';

export const AddressRawDataModule = {
  fetchsert: async (
    { city, cityMapBox, countryIso2, latitude, longitude, mapboxId, text }: AddressRawDataParams,
    filters: Prisma.AddressRawDataWhereInput,
  ): Promise<AddressRawDataResult> => {
    const select: Prisma.AddressRawDataSelect = {
      id: true,
      latitude: true,
      longitude: true,
      mapboxId: true,
      City: {
        select: {
          id: true,
          name: true,
        },
      },
      CityRawData: {
        select: {
          id: true,
          name: true,
        },
      },
    };
    let addressRawData = await prismaPG.addressRawData.findFirst({
      where: filters,
      select,
    });
    if (addressRawData) {
      return {
        ...pick(addressRawData, ['id', 'latitude', 'longitude', 'mapboxId', 'text']),
        city: Master.CityModule.transform(addressRawData),
      } as AddressRawDataResult;
    }
    let cityResult: CityClientI = null;
    const cityInput: Prisma.CityRawDataUncheckedUpdateInput = {};
    const cityFilter: Prisma.CityRawDataWhereInput = {};
    if (isFilled(city)) {
      cityFilter.id = city.id;
    } else if (isFilled(cityMapBox)) {
      cityFilter.countryIso2 = countryIso2;
      cityFilter.OR = [
        {
          mapboxId: {
            equals: cityMapBox.id,
            mode: 'insensitive',
          },
        },
        {
          name: {
            contains: cityMapBox.name,
            mode: 'insensitive',
          },
        },
      ];
      cityInput.name = cityMapBox.name;
      cityInput.latitude = latitude;
      cityInput.longitude = longitude;
    }
    if (isFilled(city)) {
      cityResult = await Master.CityModule.fetchsert(cityInput, cityFilter, city?.dataType);
    }
    const input: Prisma.AddressRawDataUncheckedCreateInput = {
      countryIso2,
      latitude,
      longitude,
      text,
      mapboxId,
    };
    if (isFilled(city)) {
      if (cityResult?.dataType === 'master') {
        input.cityId = cityResult?.id;
      } else {
        input.cityRawDataId = cityResult.id;
      }
    }
    addressRawData = await prismaPG.addressRawData.create({
      data: input,
      select,
    });
    if (!addressRawData) {
      throw new AppError('ADDR002');
    }
    return {
      ...pick(addressRawData, ['id', 'latitude', 'longitude', 'mapboxId', 'text']),
      city: Master.CityModule.transform(addressRawData),
    } as AddressRawDataResult;
  },
};
