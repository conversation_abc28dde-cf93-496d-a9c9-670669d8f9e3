import type { FastifyStateI } from '@interfaces/common/declaration';
import type { ForumCommentCreateOneI } from '@schemas/forum/comment';
import { AnswerModule } from './answer';
import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { Prisma } from '@prisma/postgres';
import type { AnswerComment } from '@prisma/postgres';
import type { RouteParamsI } from '@schemas/common/common';
import type { IdCursorIdI, TotalCursorDataI } from '@interfaces/common/data';
import User from '@modules/user';
import type { ForumCommentExternalI } from '@interfaces/forum/comment';
import { CommunityModule } from '../community/community';
import type {
  ForumAnswerCommentDeleteOneI,
  ForumAnswerCommentFetchManyI,
  ForumAnswerCommentFetchRepliesI,
} from '@schemas/forum/answerComment';
import type {
  ForumAnswerCommentFetchManyCoreReplyItemI,
  ForumAnswerCommentFetchManyReplySQLI,
  ForumAnswerCommentFetchManyResultI,
  ForumAnswerCommentFetchManySQLI,
} from '@interfaces/forum/answerComment';
import type { TotalI } from '@interfaces/common/db';
import { isNumber, pick } from '@utils/data/object';
import { isNullUndefined } from '@utils/data/data';
import { errorHandler } from '@utils/errors/handler';
import { ellipsis } from '@utils/data/string';
import type { ProfileIdI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';

export const AnswerCommentModule = {
  fetchById: async (
    { id }: RouteParamsI,
    select: Prisma.AnswerCommentSelect = {
      id: true,
    },
    isThrowingError: boolean = true,
  ): Promise<AnswerComment> => {
    const answer = await prismaPG.answerComment.findUnique({
      select,
      where: {
        id,
      },
    });
    if (isThrowingError && !answer) {
      throw new AppError('FMACMT002');
    }
    return answer;
  },
  createOne: async (
    state: FastifyStateI,
    { answerId, parentCommentId, text }: ForumCommentCreateOneI,
  ): Promise<IdCursorIdI> => {
    const selfProfileId = state.profileId;
    const [answer, parentComment] = await Promise.all([
      AnswerModule.fetchById(
        { id: answerId },
        { communityId: true, questionId: true, text: true, Question: { select: { profileId: true } } },
      ),
      parentCommentId
        ? prismaPG.answerComment.findUnique({
            where: { id: parentCommentId, answerId },
            select: { id: true, parentCommentId: true, replyCount: true, profileId: true },
          })
        : null,
    ]);
    if (parentCommentId && !parentComment) {
      throw new AppError('FMACMT003');
    }
    const questionId = answer.questionId;
    const communityId = answer.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    const input: Prisma.AnswerCommentUncheckedCreateInput = {
      questionId,
      profileId: selfProfileId,
      text,
      answerId,
      communityId,
    };
    if (parentCommentId) {
      input.parentCommentId = parentCommentId;
    }
    const [comment, _parentComment, _answer, selfProfile] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.answerComment.create({
            data: input,
            select: { id: true, cursorId: true },
          }),
          parentComment
            ? txn.answerComment.update({
                select: { replyCount: true, id: true },
                data: !isNullUndefined(parentComment?.replyCount)
                  ? {
                      replyCount: {
                        increment: 1,
                      },
                    }
                  : { replyCount: 1 },
                where: {
                  id: parentCommentId,
                },
              })
            : null,
          txn.answer.update({
            data: {
              commentCount: {
                increment: 1,
              },
            },
            where: {
              id: answerId,
            },
            select: { id: true },
          }),
          txn.profile.findUnique({
            where: {
              id: selfProfileId,
            },
            select: {
              name: true,
            },
          }),
        ]),
    );

    if (!comment) {
      throw new AppError('FMACMT001');
    }
    const profileIdsSet = new Set<string>([]);
    if (parentCommentId) {
      const profiles = await prismaPG.$queryRaw<ProfileIdI[]>`
      SELECT
        DISTINCT u."id"
      FROM "user"."Profile" u
      INNER JOIN "forum"."AnswerComment" c
        ON c."answerId" = ${answerId}
        AND c."parentCommentId" = ${parentCommentId}
        AND c."profileId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}
      WHERE u."id" != ${selfProfileId}
    `;
      if (profiles?.length) {
        profiles.forEach((profile) => profileIdsSet.add(profile.id));
      }
      if (selfProfileId !== parentComment.profileId && !profileIdsSet.has(parentComment.profileId)) {
        profileIdsSet.add(parentComment.profileId);
      }
    }
    if (selfProfileId !== answer.profileId && !profileIdsSet.has(answer.profileId)) {
      profileIdsSet.add(answer.profileId);
    }
    if (selfProfileId !== answer.Question.profileId && !profileIdsSet.has(answer.Question.profileId)) {
      profileIdsSet.add(answer.Question.profileId);
    }
    if (profileIdsSet.size) {
      await CommunicationModule.NotificationModule.createOne({
        actorProfileId: selfProfileId,
        actorProfileName: selfProfile?.name ?? 'Unknown',
        answerId: answer.id,
        questionId,
        answerText: ellipsis(answer?.text),
        commentId: comment.id,
        ...(parentCommentId ? { parentCommentId } : {}),
        topic: 'communication_topic',
        type: parentCommentId ? 'FORUM_ANSWER_REPLY' : 'FORUM_ANSWER_COMMENT',
      });
    }
    return { id: comment.id, cursorId: Number(comment.cursorId) };
  },
  fetchMany: async (
    state: FastifyStateI,
    { answerId, cursorId, pageSize }: ForumAnswerCommentFetchManyI,
  ): Promise<TotalCursorDataI<ForumAnswerCommentFetchManyResultI>> => {
    const selfProfileId = state.profileId;
    const answer = await AnswerModule.fetchById({ id: answerId }, { communityId: true, profileId: true });
    const communityId = answer.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });
    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotal] = await Promise.all([
      prismaPG.$queryRaw<ForumAnswerCommentFetchManySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."replyCount",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar",
        (
          SELECT json_agg(reply_data)
          FROM (
            SELECT
              r."id",
              r."cursorId",
              r."text",
              r."replyCount",
              r."createdAt",
              r."profileId",
              ru."name" AS "profileName",
              ru."avatar" AS "profileAvatar"
            FROM "forum"."AnswerComment" r
            INNER JOIN "user"."Profile" ru ON ru."id" = r."profileId"
            LEFT JOIN "network"."BlockedProfile" br1
              ON br1."blockerId" = ${selfProfileId}::uuid
              AND br1."blockedId" = ru."id"
            LEFT JOIN "network"."BlockedProfile" br2
              ON br2."blockerId" = ru."id"
              AND br2."blockedId" = ${selfProfileId}::uuid
            WHERE
              r."parentCommentId" = c."id"
              AND br1."blockerId" IS NULL
              AND br2."blockerId" IS NULL
            ORDER BY r."createdAt" DESC
            LIMIT 2
          ) AS reply_data
        ) AS replies
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."answerId" = ${answerId}::uuid
        AND c."parentCommentId" IS NULL
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
        AND u."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."answerId" = ${answerId}::uuid
        AND c."parentCommentId" IS NULL
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);

    const comments: ForumAnswerCommentFetchManyResultI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      comments.push(
        ...commentsTemp.map(
          (item) =>
            ({
              ...pick(item, ['id', 'text', 'replyCount', 'createdAt']),
              cursorId: Number(item.cursorId),
              canDelete: item.profileId === selfProfileId,
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
              replies:
                item?.replies?.map(
                  (reply) =>
                    ({
                      id: reply.id,
                      text: reply.text,
                      cursorId: reply.cursorId,
                      createdAt: reply.createdAt,
                      profile: {
                        id: reply.profileId,
                        name: reply.profileName,
                        avatar: reply.profileAvatar,
                      },
                    }) as ForumAnswerCommentFetchManyCoreReplyItemI,
                ) || null,
            }) as ForumAnswerCommentFetchManyResultI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        comments.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: comments,
      total: Number(commentsTotal?.[0]?.total || 0),
      nextCursorId,
    };
  },
  fetchReplies: async (
    state: FastifyStateI,
    { answerId, parentCommentId, cursorId, pageSize }: ForumAnswerCommentFetchRepliesI,
  ): Promise<TotalCursorDataI<ForumAnswerCommentFetchManyCoreReplyItemI>> => {
    const selfProfileId = state.profileId;

    const [answer, _parentComment] = await Promise.all([
      AnswerModule.fetchById({ id: answerId }, { communityId: true, profileId: true }),
      parentCommentId ? AnswerCommentModule.fetchById({ id: parentCommentId }) : null,
    ]);

    const communityId = answer.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }

    const [commentsTemp, commentsTotal] = await Promise.all([
      prismaPG.$queryRaw<ForumAnswerCommentFetchManyReplySQLI[]>`
      SELECT
        c."id",
        c."cursorId",
        c."text",
        c."createdAt",
        c."profileId",
        u."name" AS "profileName",
        u."avatar" AS "profileAvatar"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        ${cursorId ? Prisma.sql` AND c."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY c."createdAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1) AS "total"
      FROM
        "forum"."AnswerComment" c
      INNER JOIN "user"."Profile" u
        ON u."id" = c."profileId"
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        c."parentCommentId" = ${parentCommentId}::uuid
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
    `,
    ]);
    const comments: ForumAnswerCommentFetchManyCoreReplyItemI[] = [];
    let nextCursorId = null;
    if (commentsTemp?.length) {
      comments.push(
        ...commentsTemp.map(
          (item) =>
            ({
              id: item.id,
              text: item.text,
              createdAt: item.createdAt,
              cursorId: Number(item.cursorId),
              profile: {
                id: item.profileId,
                name: item.profileName,
                avatar: item.profileAvatar,
              },
              canDelete: item.profileId === selfProfileId,
            }) as ForumAnswerCommentFetchManyCoreReplyItemI,
        ),
      );
      const lastComment = commentsTemp[commentsTemp.length - 1];
      nextCursorId =
        comments.length > 0 && lastComment
          ? Number(lastComment.cursorId)
          : typeof cursorId === 'number'
            ? cursorId
            : null;
    }
    return {
      data: comments,
      total: Number(commentsTotal?.[0]?.total || 0),
      nextCursorId,
    };
  },
  deleteOne: async (state: FastifyStateI, { id: commentId }: ForumAnswerCommentDeleteOneI): Promise<void> => {
    try {
      const selfProfileId = state.profileId;

      const existingComment = await prismaPG.answerComment.findUnique({
        where: { id: commentId },
        select: {
          communityId: true,
          profileId: true,
          replyCount: true,
          Answer: {
            select: {
              id: true,
              commentCount: true,
              Community: {
                select: {
                  CommunityMember: {
                    select: { type: true },
                    where: {
                      profileId: selfProfileId,
                      type: {
                        in: ['ADMIN', 'MODERATOR'],
                      },
                    },
                  },
                },
              },
            },
          },
          Parent: {
            select: { id: true, replyCount: true },
          },
        },
      });
      if (!existingComment) {
        throw new AppError('FQCM003');
      }
      const communityId = existingComment.communityId;
      await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

      const isCommentAuthor = existingComment.profileId === selfProfileId;

      const isCommunityAdminOrModerator = existingComment.Answer.Community.CommunityMember.length > 0;

      if (!(isCommentAuthor || isCommunityAdminOrModerator)) {
        throw new AppError('FMACMT007');
      }
      const [deletedComment, _updatedAnswer, _updatedParentComment] = await prismaPG.$transaction(
        async (txn) =>
          await Promise.all([
            txn.answerComment.delete({
              select: { id: true },
              where: { id: commentId },
            }),
            existingComment.Answer?.commentCount > 0
              ? txn.answer.update({
                  data: {
                    commentCount: {
                      decrement: isNumber(existingComment.replyCount) ? existingComment.replyCount + 1 : 1,
                    },
                  },
                  select: { commentCount: true },
                  where: {
                    id: existingComment.Answer.id,
                  },
                })
              : null,
            existingComment?.Parent?.id && existingComment?.Parent?.replyCount > 0
              ? txn.answerComment.update({
                  data: {
                    replyCount: { decrement: 1 },
                  },
                  where: {
                    id: existingComment.Parent?.id,
                  },
                })
              : null,
          ]),
      );
      if (!deletedComment) {
        throw new AppError('FQCM008');
      }
    } catch (error) {
      errorHandler(error);
    }
  },
  transformComment: (item): ForumCommentExternalI => ({
    id: item.id,
    text: item.text,
    cursorId: Number(item.cursorId.toString()),
    replyCount: item.replyCount,
    createdAt: item.createdAt,
    Profile: User.ProfileModule.transformProfile(item.Profile),
  }),
};
