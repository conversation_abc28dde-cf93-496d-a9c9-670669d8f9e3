import { prismaPG } from '@config/db';
import { NearByFetchPeopleItemI, NearByFetchPeopleItemSQLI } from '@interfaces/announcement/nearBy';
import { AnnouncementNearByConfigI } from '@interfaces/appConfig/appConfig';
import { CursorDataI, NumberNullI } from '@interfaces/common/data';
import type { FastifyStateI } from '@interfaces/common/declaration';
import AppConfig from '@modules/appConfig';
import Auth from '@modules/auth';
import User from '@modules/user';
import { Prisma } from '@prisma/postgres';
import type { NearByFetchPeopleParamsI } from '@schemas/announcement/nearBy';

export const NearByModule = {
  fetchPeople: async (
    state: FastifyStateI,
    { cursorId, latitude, longitude, pageSize }: NearByFetchPeopleParamsI,
  ): Promise<CursorDataI<NearByFetchPeopleItemI>> => {
    const [_updatedSession, nearByConfig] = (await Promise.all([
      Auth.SessionModule.updateCoordinates(state, { latitude, longitude }),
      AppConfig.AppConfigModule.fetchByIdWithFallback(
        { module: 'ANNOUNCEMENT', subModule: 'NEAR_BY' },
        undefined,
        'web_app',
      ),
    ])) as [void, AnnouncementNearByConfigI];
    const radiusKM: number = nearByConfig.profileRadiusInKM;
    const selfProfileId = state.profileId;
    const nearByFetchPeopleItemsTemp: NearByFetchPeopleItemSQLI[] = await prismaPG.$queryRaw<
      NearByFetchPeopleItemSQLI[]
    >`
        SELECT DISTINCT
          u."id",
          u."cursorId",
          u."name",
          u."avatar",
          u."designationText",
          u."designationAlternativeId",
          u."designationRawDataId",
          u."entityText",
          u."entityId",
          u."entityRawDataId",
          s."longitude",
          s."latitude",
          ST_Distance(
              ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
              ST_SetSRID(ST_MakePoint(s."longitude", s."latitude"), 4326)::geography
          ) AS distanceinmeters
      FROM "auth"."Session" s
      INNER JOIN "user"."Profile" u
          ON u."id" = s."profileId"
          AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      LEFT JOIN "network"."BlockedProfile" b1
          ON b1."blockerId" = ${selfProfileId}::uuid
          AND b1."blockedId" = u."id"
      LEFT JOIN "network"."BlockedProfile" b2
          ON b2."blockerId" = u."id"
          AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE s."isActive" = TRUE
          AND s."latitude" IS NOT NULL
          AND s."longitude" IS NOT NULL
          AND u."id" != ${selfProfileId}::uuid
          AND ST_DWithin(
              ST_SetSRID(ST_MakePoint(${longitude}, ${latitude}), 4326)::geography,
              ST_SetSRID(ST_MakePoint(s."longitude", s."latitude"), 4326)::geography,
              ${radiusKM * 1000}
          )
          ${cursorId ? Prisma.sql` AND u."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY distanceInMeters ASC, u."cursorId" DESC
      LIMIT ${pageSize + 1}
      `;
    const hasNextPage = nearByFetchPeopleItemsTemp.length > pageSize;
    const results = hasNextPage ? nearByFetchPeopleItemsTemp.slice(0, pageSize) : nearByFetchPeopleItemsTemp;

    const nextCursorId: NumberNullI = hasNextPage ? results[results.length - 1].cursorId : null;
    const nearByFetchPeopleItems: NearByFetchPeopleItemI[] = [];
    if (results?.length) {
      nearByFetchPeopleItems.push(
        ...results.map(
          (item) =>
            ({
              ...User.ProfileModule.transformProfile(item),
              distanceInMeters: Number(item.distanceinmeters),
              latitude: item.latitude,
              longitude: item.longitude,
              cursorId: item.cursorId.toString(),
            }) as NearByFetchPeopleItemI,
        ),
      );
    }
    return {
      data: nearByFetchPeopleItems,
      nextCursorId,
    };
  },
};
