import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import {
  FetchByUnLocodeDetailedRawDataI,
  FetchByUnLocodeDetailedResultI,
  PortClientI,
  PortRawQueryFetchForClientResultI,
  PortRawQueryFetchsertResultI,
  PortSearchClientI,
} from '@interfaces/port/port';
import { CityNestedClientI } from '@interfaces/master/city';
import Master from '@modules/master';
import { Prisma } from '@prisma/postgres';
import { PaginationI } from '@schemas/common/common';
import { PortModuleFetchsertParamsI, PortUnLocodeTypeI } from '@schemas/port/port';
import { pick } from '@utils/data/object';
import { FastifyStateI } from '@interfaces/common/declaration';

export const PortModule = {
  fetchByUnLocode: async (filters: PortUnLocodeTypeI, _isThrowingError: boolean = true): Promise<PortClientI> => {
    const portResult = await prismaPG.$queryRaw<PortClientI[]>`
        WITH master_port AS (
      SELECT
        p."unLocode",
        'raw' AS "dataType"
      FROM "port"."Port" p
      WHERE p."unLocode" = ${filters.unLocode}
        AND ${filters.dataType === 'master'}
      LIMIT 1
    ),
      raw_port AS (
      SELECT
        prd."unLocode",
        'raw' AS "dataType"
      FROM "rawData"."PortRawData" prd
      WHERE prd."unLocode" = ${filters.unLocode}
        AND ${filters.dataType === 'raw'}
      LIMIT 1
    )
    SELECT * FROM master_port
    UNION ALL
    SELECT * FROM raw_port
    LIMIT 1
  `;

    if (portResult?.length) {
      return portResult[0];
    }
    if (_isThrowingError) {
      throw new AppError('PORT001');
    }
  },
  fetchByUnLocodeDetailed: async (
    state: FastifyStateI,
    { unLocode, dataType }: PortUnLocodeTypeI,
    _isThrowingError: boolean = true,
  ): Promise<FetchByUnLocodeDetailedResultI> => {
    const selfProfileId = state.profileId;
    const portResultArr = await prismaPG.$queryRaw<FetchByUnLocodeDetailedRawDataI[]>`
      ${
        dataType === 'master'
          ? Prisma.sql`
          SELECT
            p."unLocode",
            p."name",
            p."imageUrl",
            p."latitude",
            p."longitude",
            p."noOfTerminals",
            p."noOfBerths",
            p."maxDraught",
            p."maxDeadweight",
            p."maxLength",
            p."maxAirDraught",
            p."countryIso2",
            c."name" AS "countryName",
            t."id" AS "cityId",
            t."name" AS "cityName",
            tz."timezone" AS "timezone",
            tz."utcOffset" AS "utcOffset",
            tz."dstOffset" AS "dstOffset",
            'master' AS "dataType",
            EXISTS (
              SELECT 1
              FROM "port"."PortVisitor" v
              WHERE v."portUnLocode" = p."unLocode"
                AND v."profileId" = ${selfProfileId}::uuid
            ) AS "isVisited"
          FROM "port"."Port" p
          INNER JOIN "master"."Country" c ON c."iso2" = p."countryIso2"
          LEFT JOIN "master"."City" t ON t."id" = p."cityId"
          LEFT JOIN "master"."Timezone" tz ON tz."timezone" = p."timezone" AND tz."countryIso2" = p."countryIso2"
          WHERE p."unLocode" = ${unLocode}
        `
          : Prisma.sql`
          SELECT
            prw."unLocode",
            prw."name",
            NULL AS "imageUrl",
            prw."latitude",
            prw."longitude",
            prw."noOfTerminals",
            prw."noOfBerths",
            prw."maxDraught",
            prw."maxDeadweight",
            prw."maxLength",
            prw."maxAirDraught",
            prw."countryIso2",
            c."name" AS "countryName",
            prw."cityId",
            t."name" AS "cityName",
            prw."cityRawDataId",
            trw."name" AS "cityRawDataName",
            tz."timezone" AS "timezone",
            tz."utcOffset" AS "utcOffset",
            tz."dstOffset" AS "dstOffset",
            'raw' AS "dataType",
            EXISTS (
              SELECT 1
              FROM "port"."PortVisitor" v
              WHERE v."portUnLocode" = prw."unLocode"
                AND v."profileId" = ${selfProfileId}::uuid
            ) AS "isVisited"
          FROM "rawData"."PortRawData" prw
          INNER JOIN "master"."Country" c ON c."iso2" = prw."countryIso2"
          LEFT JOIN "master"."City" t ON t."id" = prw."cityId"
          LEFT JOIN "rawData"."CityRawData" trw ON trw."id" = prw."cityRawDataId"
          LEFT JOIN "master"."Timezone" tz ON tz."timezone" = prw."timezone" AND tz."countryIso2" = prw."countryIso2"
          WHERE prw."unLocode" = ${unLocode}
        `
      }
    `;

    if (portResultArr?.length) {
      const portResultTemp = portResultArr[0];
      const portResult: FetchByUnLocodeDetailedResultI = {
        unLocode: portResultTemp.unLocode,
        name: portResultTemp.name,
        imageUrl: portResultTemp.imageUrl,
        city: portResultTemp.cityId
          ? { name: portResultTemp.cityName }
          : portResultTemp.cityRawDataId
            ? { name: portResultTemp.cityRawDataName }
            : null,
        country: {
          name: portResultTemp.countryName,
        },
        latitude: portResultTemp.latitude,
        longitude: portResultTemp.longitude,
        noOfTerminals: portResultTemp.noOfTerminals,
        noOfBerths: portResultTemp.noOfBerths,
        maxDraught: portResultTemp.maxDraught,
        maxDeadweight: portResultTemp.maxDeadweight,
        maxLength: portResultTemp.maxLength,
        maxAirDraught: portResultTemp.maxAirDraught,
        dataType: portResultTemp.dataType,
        isVisited: portResultTemp.isVisited ?? false,
        timezone: {
          timezone: portResultTemp?.timezone,
          utcOffset: portResultTemp?.utcOffset,
          dstOffset: portResultTemp?.dstOffset,
        },
      };
      return portResult;
    }
    if (_isThrowingError) {
      throw new AppError('PORT001');
    }
  },
  fetchForClient: async (
    search: string,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: PortSearchClientI[]; total: number }> => {
    search = search.trim().toLowerCase();
    const offset = pagination.page * pagination.pageSize;
    const limit = pagination.pageSize;

    const portRawQueryResult: PortRawQueryFetchForClientResultI[] = await prismaPG.$queryRaw<
      PortRawQueryFetchForClientResultI[]
    >`
    WITH master_ports AS (
      SELECT
        p."unLocode",
        p."name",
        c."iso2" AS "countryIso2",
        c."name" AS "countryName",
        city."id" AS "cityId",
        city."name" AS "cityName",
        NULL::uuid AS "cityRawDataId",
        NULL::text AS "cityRawName",
        'master' AS "dataType",
        city."name" AS "sortName"
      FROM port."Port" p
      LEFT JOIN master."Country" c ON p."countryIso2" = c."iso2"
      LEFT JOIN master."City" city ON p."cityId" = city."id"
      WHERE
        p."name" ILIKE ${search + '%'}
        OR p."unLocode" ILIKE ${search + '%'}
        OR city."name" ILIKE ${search + '%'}
    ),
    raw_ports AS (
      SELECT
        prd."unLocode",
        prd."name",
        c."iso2" AS "countryIso2",
        c."name" AS "countryName",
        city."id" AS "cityId",
        city."name" AS "cityName",
        crd."id" AS "cityRawDataId",
        crd."name" AS "cityRawName",
        'raw' AS "dataType",
        COALESCE(city."name", crd."name") AS "sortName"
      FROM "rawData"."PortRawData" prd
      LEFT JOIN master."Country" c ON prd."countryIso2" = c."iso2"
      LEFT JOIN master."City" city ON prd."cityId" = city."id"
      LEFT JOIN "rawData"."CityRawData" crd ON prd."cityRawDataId" = crd."id"
      WHERE
        LOWER(prd."name") ILIKE ${search} || '%'
        OR LOWER(prd."unLocode") ILIKE ${search} || '%'
        OR LOWER(city."name") ILIKE ${search} || '%'
        OR LOWER(crd."name") ILIKE ${search} || '%'
    ),
    combined_results AS (
      SELECT * FROM master_ports
      UNION ALL
      SELECT * FROM raw_ports
    )
    SELECT * FROM combined_results
    ORDER BY "sortName" ASC
    LIMIT ${limit} OFFSET ${offset}
    `;

    const totalResult: { count: number }[] = await prismaPG.$queryRaw<{ count: number }[]>`
    WITH master_ports AS (
      SELECT 1
      FROM port."Port" p
      LEFT JOIN master."Country" c ON p."countryIso2" = c."iso2"
      LEFT JOIN master."City" city ON p."cityId" = city."id"
      WHERE
        p."name" ILIKE ${search + '%'}
        OR p."unLocode" ILIKE ${search + '%'}
        OR city."name" ILIKE ${search + '%'}
    ),
    raw_ports AS (
      SELECT 1
      FROM "rawData"."PortRawData" prd
      LEFT JOIN master."Country" c ON prd."countryIso2" = c."iso2"
      LEFT JOIN master."City" city ON prd."cityId" = city."id"
      LEFT JOIN "rawData"."CityRawData" crd ON prd."cityRawDataId" = crd."id"
      WHERE
        LOWER(prd."name") ILIKE ${search} || '%'
        OR LOWER(prd."unLocode") ILIKE ${search} || '%'
        OR LOWER(city."name") ILIKE ${search} || '%'
        OR LOWER(crd."name") ILIKE ${search} || '%'
    )
    SELECT COUNT(*)::int AS count FROM (
      SELECT * FROM master_ports
      UNION ALL
      SELECT * FROM raw_ports
    ) combined_count
    `;

    const portClientResult: PortSearchClientI[] = [];
    if (portRawQueryResult?.length) {
      portClientResult.push(
        ...portRawQueryResult.map((port) => {
          const city: CityNestedClientI = (
            port?.cityId?.length
              ? { id: port.cityId, name: port.cityName, dataType: 'master' }
              : { id: port.cityRawDataId, name: port.cityRawName, dataType: 'raw' }
          ) as CityNestedClientI;
          const portSearchClient: PortSearchClientI = {
            unLocode: port.unLocode,
            name: port.name,
            city,
            dataType: port.dataType,
          };
          return portSearchClient;
        }),
      );
    }

    return {
      data: portClientResult,
      total: totalResult?.[0]?.count ?? 0,
    };
  },
  fetchPopularPorts: async (_state: FastifyStateI): Promise<{ data: PortSearchClientI[]; total: number }> => {
    const [topPorts, totalResult] = await Promise.all([
      prismaPG.$queryRaw<
        {
          unLocode: string;
          name: string;
          countryName: string | null;
          cityId: string | null;
          cityName: string | null;
          cityRawDataId: string | null;
          cityRawName: string | null;
          dataType: 'master' | 'raw';
          visitorCount: number;
        }[]
      >(Prisma.sql`
      SELECT
        COALESCE(pv."portUnLocode", pv."portRawDataUnLocode") AS "unLocode",
        COALESCE(p."name", prd."name") AS "name",
        COALESCE(city."name", crd."name") AS "cityName",
        city."id" AS "cityId",
        crd."id" AS "cityRawDataId",
        crd."name" AS "cityRawName",
        CASE
          WHEN pv."portUnLocode" IS NOT NULL THEN 'master'
          ELSE 'raw'
        END AS "dataType",
        COUNT(DISTINCT pv."profileId") AS "visitorCount"
      FROM port."PortVisitor" pv
      LEFT JOIN port."Port" p ON pv."portUnLocode" = p."unLocode"
      LEFT JOIN "rawData"."PortRawData" prd ON pv."portRawDataUnLocode" = prd."unLocode"
      LEFT JOIN master."City" city ON p."cityId" = city."id"
      LEFT JOIN "rawData"."CityRawData" crd ON prd."cityRawDataId" = crd."id"
      INNER JOIN "user"."Profile" u ON u."id" = pv."profileId" AND u."status" = 'ACTIVE'
      GROUP BY
        COALESCE(pv."portUnLocode", pv."portRawDataUnLocode"),
        COALESCE(p."name", prd."name"),
        COALESCE(city."name", crd."name"),
        city."id",
        crd."id",
        crd."name",
        CASE
          WHEN pv."portUnLocode" IS NOT NULL THEN 'master'
          ELSE 'raw'
        END
      ORDER BY "visitorCount" DESC
      LIMIT 5
    `),

      prismaPG.$queryRaw<{ total: number }[]>(Prisma.sql`
      SELECT COUNT(*)::int AS total FROM (
        SELECT
          COALESCE(pv."portUnLocode", pv."portRawDataUnLocode") AS "unLocode"
        FROM port."PortVisitor" pv
        INNER JOIN "user"."Profile" u ON u."id" = pv."profileId" AND u."status" = 'ACTIVE'
        GROUP BY COALESCE(pv."portUnLocode", pv."portRawDataUnLocode")
      ) AS unique_ports
    `),
    ]);

    const data: PortSearchClientI[] = topPorts.map((port) => {
      const city: CityNestedClientI = port.cityId
        ? { id: port.cityId, name: port.cityName ?? '', dataType: 'master' }
        : { id: port.cityRawDataId ?? '', name: port.cityRawName ?? '', dataType: 'raw' };

      return {
        unLocode: port.unLocode,
        name: port.name,
        city,
        dataType: port.dataType,
      };
    });

    return {
      data,
      total: totalResult?.[0]?.total ?? 0,
    };
  },
  fetchsert: async (params: PortModuleFetchsertParamsI): Promise<PortSearchClientI> => {
    params.unLocode = params.unLocode.trim().toUpperCase();
    const portRawQueryResult: PortRawQueryFetchsertResultI[] = await prismaPG.$queryRaw<PortRawQueryFetchsertResultI[]>`
    WITH CombinedPorts AS (
      SELECT
        p."unLocode",
        p."name",
        c."iso2" AS "countryIso2",
        c."name" AS "countryName",
        city."id" AS "cityId",
        city."name" AS "cityName",
        NULL::uuid AS "cityRawDataId",
        NULL::text AS "cityRawName",
        'master' AS "dataType"
      FROM port."Port" p
      LEFT JOIN master."Country" c ON p."countryIso2" = c."iso2"
      LEFT JOIN master."City" city ON p."cityId" = city."id"
      WHERE UPPER(p."unLocode") = ${params.unLocode}

      UNION

      SELECT
        prd."unLocode",
        prd."name",
        c."iso2" AS "countryIso2",
        c."name" AS "countryName",
        city."id" AS "cityId",
        city."name" AS "cityName",
        crd."id" AS "cityRawDataId",
        crd."name" AS "cityRawName",
        'raw' AS "dataType"
      FROM "rawData"."PortRawData" prd
      LEFT JOIN master."Country" c ON prd."countryIso2" = c."iso2"
      LEFT JOIN  master."City" city ON prd."cityId" = city."id"
      LEFT JOIN "rawData"."CityRawData" crd ON prd."cityRawDataId" = crd."id"
      WHERE UPPER(prd."unLocode") = ${params.unLocode}
    )
    SELECT * FROM CombinedPorts
    ORDER BY "dataType" ASC
    LIMIT 1
`;
    if (portRawQueryResult?.length) {
      return {
        ...pick(portRawQueryResult[0], ['unLocode', 'name', 'dataType']),
        city: { id: portRawQueryResult[0].cityId, name: portRawQueryResult[0].cityName },
      } as PortSearchClientI;
    }
    const cityResult = await Master.CityModule.fetchByOne({ city: params.city });
    const portRawDataKeysSet: Set<string> = new Set<string>([
      'unLocode',
      'name',
      'countryIso2',
      'timezoneIso2',
      'latitude',
      'longitude',
      'noOfTerminals',
      'noOfBerths',
      'maxDraught',
      'maxDeadweight',
      'maxLength',
      'maxAirDraught',
    ]);
    const portRawDataInput: Prisma.PortRawDataUncheckedCreateInput = Object.keys(params).reduce((acc, key) => {
      if (portRawDataKeysSet.has(key)) {
        acc[key] = params[key];
      }
      return acc;
    }, {} as Prisma.PortRawDataUncheckedCreateInput);
    if (params.city?.dataType === 'master') {
      portRawDataInput.cityId = params.city.id;
    } else if (params.city?.dataType === 'raw') {
      portRawDataInput.cityRawDataId = params.city.id;
    }
    const portRawDataResult = await prismaPG.portRawData.create({ data: portRawDataInput });
    if (!portRawDataResult?.unLocode) {
      throw new AppError('PORT002');
    }
    return {
      unLocode: params.unLocode,
      name: params.name,
      city: {
        id: cityResult.id,
        name: cityResult.name,
        dataType: params.city.dataType,
      },
      dataType: 'raw' as DBDataTypeI,
    } as PortSearchClientI;
  },
};
