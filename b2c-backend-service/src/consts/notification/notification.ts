import { z } from 'zod';
import { NotificationTypeE as NotificationType } from '@prisma/mongodb';

export const NotificationTypeE = z.enum([
  NotificationType.COMMENT,
  NotificationType.FOLLOWER,
  NotificationType.FORUM_QUESTION_LIVE,
  NotificationType.FORUM_QUESTION_VOTE,
  NotificationType.FORUM_QUESTIONS,
  NotificationType.FORUM_ANSWER,
  NotificationType.FORUM_ANSWER_VOTE,
  NotificationType.FORUM_QUESTION_COMMENT,
  NotificationType.FORUM_QUESTION_REPLY,
  NotificationType.FORUM_ANSWER_COMMENT,
  NotificationType.FORUM_ANSWER_REPLY,
  NotificationType.FORUM_ANSWER_VERIFIED,
  NotificationType.LIKE,
  NotificationType.MESSAGE,
  NotificationType.PUBLIC,
  NotificationType.REPLY,
  NotificationType.REQUEST_ACCEPTED,
  NotificationType.REQUEST_RECEIVED,
  NotificationType.UPDATE_READ,
]);
export type NotificationTypeI = z.infer<typeof NotificationTypeE>;

export const NotificationTopicE = z.enum(['public', 'marketing']);
export type NotificationTopicI = z.infer<typeof NotificationTopicE>;

export const NotificationScreenE = z.enum(['ForumScreen']);
export type NotificationScreenI = z.infer<typeof NotificationScreenE>;
