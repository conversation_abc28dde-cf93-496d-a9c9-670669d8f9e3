import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { UnLocodeTypeSchema } from '@schemas/port/common';
import { PortFetchSchema, PortModuleFetchsertParamsSchema } from '@schemas/port/port';
import { pick } from '@utils/data/object';
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const portChildRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/port/search', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = PortFetchSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PORT005', queryError);
    }
    const result = await Port.PortModule.fetchForClient(queryData.search, pick(queryData, ['page', 'pageSize']));
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/port/port/popular', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await Port.PortModule.fetchPopularPorts(request);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/port/port/single', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = UnLocodeTypeSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PORT005', queryError);
    }
    const result = await Port.PortModule.fetchByUnLocodeDetailed(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/port/port', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data, error } = PortModuleFetchsertParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('PORT006');
    }
    const result = await Port.PortModule.fetchsert(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default portChildRoutes;
