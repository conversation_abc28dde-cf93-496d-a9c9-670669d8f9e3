import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import {
  ShipFetchForClientSchema,
  ShipFetchsertParamsSchema,
  ShipImoClientSchema,
  ShipSearchSchema,
} from '@schemas/ship/ship';
import { pick } from '@utils/data/object';

import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const shipChildRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/search', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ShipSearchSchema.safeParse(request.query);
    if (error) {
      throw new AppError('SHIP005', error);
    }
    const result = await Ship.CoreShipModule.search(data);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/ship/popular', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await Ship.CoreShipModule.fetchPopularShips(request);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/ship/multiple', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipFetchForClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHIP005', queryError);
    }
    const result = await Ship.CoreShipModule.fetchForClient(
      pick(queryData, ['search']),
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/ship/single', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipImoClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHIP005', queryError);
    }
    const result = await Ship.CoreShipModule.fetchByImoDetailed(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/ship/pre-populate', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipImoClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHIP005', queryError);
    }
    const result = await Ship.CoreShipModule.fetchPrePopulate(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/ship/ship', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data, error } = ShipFetchsertParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('SHIP006');
    }
    const result = await Ship.CoreShipModule.fetchsert(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default shipChildRoutes;
