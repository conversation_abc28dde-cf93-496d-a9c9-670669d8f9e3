import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { SlugParamSchema } from '@schemas/forum/answer';
import type { FastifyInstance, FastifyReply } from 'fastify';

const slugRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/forum/question/slug/:slug',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = SlugParamSchema.safeParse(request.params);
      if (error) {
        throw new AppError('FMQUE011', error);
      }

      const result = await ForumModule.QuestionModule.fetchBySlug(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get('/backend/api/v1/forum/answer/slug/:slug', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = SlugParamSchema.safeParse(request.params);
    if (error) {
      throw new AppError('FMANS006', error);
    }

    const result = await ForumModule.AnswerModule.fetchBySlug(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default slugRoutes;
