import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { RouteParamsSchema } from '@schemas/common/common';
import type { FastifyInstance, FastifyReply } from 'fastify';

const questionNoAuthRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/forum/question/:id/noauth',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: error, data: data } = RouteParamsSchema.safeParse(request.params);
      if (error) {
        throw new AppError('FMQUE011', error);
      }
      const result = await ForumModule.QuestionModule.fetchOneForClientNoAuth(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default questionNoAuthRoutes;
