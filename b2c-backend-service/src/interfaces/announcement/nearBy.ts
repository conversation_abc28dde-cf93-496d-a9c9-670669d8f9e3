import { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';
import { SessionUpdateCoordinatesParamsI } from '@schemas/auth/session';

export type NearByFetchPeopleItemSQLI = ProfileDataI &
  SessionUpdateCoordinatesParamsI & {
    cursorId: number;
    distanceinmeters: number;
  };

export type NearByFetchPeopleItemI = ProfileExternalI &
  SessionUpdateCoordinatesParamsI & {
    cursorId: string;
    distanceInMeters: number;
  };
