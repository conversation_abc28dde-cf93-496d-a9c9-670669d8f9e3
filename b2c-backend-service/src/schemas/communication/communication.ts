import { CommunicationCategoryE, FirebaseFCMTopicSchema } from '@consts/communication/common';
import { UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';
import { NotificationCreateOneSchema } from './notification';

export const CommunicationEmailSMSOneSchema = z.object({
  category: z.enum([CommunicationCategoryE.Values.EMAIL, CommunicationCategoryE.Values.SMS]),
});
export type CommunicationEmailSMSOneI = z.infer<typeof CommunicationEmailSMSOneSchema>;

export const CommunicationNotificationBaseSchema = z.object({
  category: z.enum([CommunicationCategoryE.Values.NOTIFICATION]),
  profileId: UUIDSchema.optional(),
  profileIds: z.array(UUIDSchema).min(1).optional(),
  firebaseTopic: FirebaseFCMTopicSchema.optional(),
  notification: NotificationCreateOneSchema,
});
export type CommunicationNotificationBaseI = z.infer<typeof CommunicationNotificationBaseSchema>;

export const CommunicationNotificationSchema = CommunicationNotificationBaseSchema.superRefine((data, ctx) => {
  if (!data?.category && (!Array.isArray(data?.profileIds) || !data?.profileIds?.length || !data?.firebaseTopic)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Either profileId or profileIds or firebaseTopic is required',
    });
  }
});
export type CommunicationNotificationI = z.infer<typeof CommunicationNotificationSchema>;

export const CommunicationUpsertMessageSchema = z.discriminatedUnion('category', [
  CommunicationEmailSMSOneSchema,
  CommunicationNotificationBaseSchema,
]);

export type CommunicationUpsertMessageI = z.infer<typeof CommunicationUpsertMessageSchema>;
