import { RSVPE } from '@consts/announcement/rsvp';
import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const AnnouncementCreateOneSchema = z.object({
  id: UUIDSchema,
  status: RSVPE,
});
export type NearByFetchPeopleParamsI = z.infer<typeof AnnouncementCreateOneSchema>;

export const RSVPUpsertSchema = z.object({
  id: UUIDSchema,
  status: RSVPE,
});
export type RSVPUpsertInputI = z.infer<typeof RSVPUpsertSchema>;
export const RSVPFetchPeopleSchema = CursorPaginationSchema.extend({
  id: UUIDSchema,
});

export type RSVPFetchPeopleI = z.infer<typeof RSVPFetchPeopleSchema>;
