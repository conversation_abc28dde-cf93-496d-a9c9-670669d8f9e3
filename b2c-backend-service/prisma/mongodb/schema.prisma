generator client {
  provider = "prisma-client-js"
  output   = "../../node_modules/@prisma/mongodb"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGO_DATABASE_URL")
}

//#region appConfig
enum ModuleE {
  ANNOUNCEMENT
  AUTH
  COMMUNICATION
  FORUM
}

enum SubModuleE {
  COMMUNICATION
  NEAR_BY
  SESSION
  QUESTION
}

model AppConfig {
  id        String     @map("_id") @db.ObjectId
  module    ModuleE
  subModule SubModuleE
  config    Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([id])
  @@unique([module, subModule])
}

//#endregion appConfig
//#region communication
enum CommunicationModeE {
  EMAIL
  SMS
}

enum CommunicationTypeE {
  EMAIL_ID_PASSWORD_RESET
  EMAIL_ID_VERIFICATION
  PHONE_VERIFICATION
}

model CommunicationTemplate {
  id   String             @map("_id") @db.ObjectId
  mode CommunicationModeE
  type CommunicationTypeE
  // For EMAIL,
  // data: { "subject": "some subject data", "content": "<html><body>Hello {{name}}, email body</body></html>" }
  // For SMS,
  // data: {content: "Your OTP is {{code}}"}
  data Json

  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  Communication Communication[]

  @@id([id])
  @@unique([mode, type])
}

enum CommunicationStatusE {
  CREATED
  PENDING
  PARTIAL_SUCCESS
  SUCCESS
  FAILED
}

model Communication {
  id         String               @default(auto()) @map("_id") @db.ObjectId
  mode       CommunicationModeE
  type       CommunicationTypeE
  templateId String               @db.ObjectId
  externalId String?
  params     Json?
  status     CommunicationStatusE @default(CREATED)
  sender     String
  receiver   String
  profileId  String
  createdAt  DateTime             @default(now())
  updatedAt  DateTime             @updatedAt

  CommunicationTemplate     CommunicationTemplate       @relation(fields: [templateId], references: [id])
  CommunicationVerification CommunicationVerification[]

  @@id([id])
}

enum CommunicationVerificationStatusE {
  CREATED
  DELIVERY_SUCCESSFUL
  DELIVERY_FAILURE
  VERIFIED
  EXPIRED
}

model CommunicationVerification {
  id              String                           @default(auto()) @map("_id") @db.ObjectId
  communicationId String                           @db.ObjectId
  // otp/token/link
  data            String                           @db.String
  status          CommunicationVerificationStatusE @default(CREATED)
  expiryDate      DateTime
  profileId       String
  createdAt       DateTime                         @default(now())
  updatedAt       DateTime                         @updatedAt

  Communication Communication @relation(fields: [communicationId], references: [id])

  @@id([id])
  @@unique([communicationId])
}

//#endregion communication

//#region notification
enum NotificationTypeE {
  FORUM_QUESTION_LIVE
  FORUM_QUESTION_VOTE
  FORUM_QUESTIONS
  FORUM_ANSWER
  FORUM_ANSWER_VOTE
  FORUM_QUESTION_COMMENT
  FORUM_QUESTION_REPLY
  FORUM_ANSWER_COMMENT
  FORUM_ANSWER_REPLY
  FORUM_ANSWER_VERIFIED
  PUBLIC
  MESSAGE
  LIKE
  COMMENT
  REPLY
  FOLLOWER
  REQUEST_RECEIVED
  REQUEST_ACCEPTED
  UPDATE_READ
}

model NotificationTemplate {
  id      String            @map("_id") @db.ObjectId
  content Json
  type    NotificationTypeE

  createdAt    DateTime       @default(now()) @db.Timestamp
  updatedAt    DateTime       @updatedAt @db.Timestamp
  Notification Notification[]

  @@id([id])
}

enum NotificationStatusE {
  PENDING
  PARTIAL_SUCCESS
  SUCCESS
  FAILED
}

model Notification {
  id                String              @default(auto()) @map("_id") @db.ObjectId
  type              NotificationTypeE
  variables         Json
  templateId        String              @db.ObjectId
  receiverProfileId String
  status            NotificationStatusE @default(PENDING)
  isRead            Boolean             @default(false)

  createdAt DateTime @default(now()) @db.Timestamp
  updatedAt DateTime @updatedAt @db.Timestamp

  NotificationTemplate NotificationTemplate @relation(fields: [templateId], references: [id])

  @@id([id])
  @@index([type])
  @@index([receiverProfileId])
}

//#endregion notification

//#region session
model Session {
  id          String  @default(auto()) @map("_id") @db.ObjectId
  sessionId   String
  profileId   String
  isActive    Boolean @default(true)
  deviceToken String

  createdAt DateTime @default(now()) @db.Timestamp
  updatedAt DateTime @updatedAt @db.Timestamp

  @@id([id])
  @@unique([sessionId])
  @@index([profileId, isActive])
}

//#endregion session

//#region service
enum VendorNameE {
  BREVO
  FIREBASE
  GOOGLE
}

enum VendorTypeE {
  COMMUNICATION
  FCM
  GOOGLE_SERVICE
}

model Vendor {
  id          String      @map("_id") @db.ObjectId
  name        VendorNameE // unique creates an index
  type        VendorTypeE
  config      Json
  credsConfig String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@id([id])
  @@unique([name])
  @@index([type])
}

enum InternalServiceNameE {
  COMMUNICATION
}

model InternalService {
  id          String               @map("_id") @db.ObjectId
  name        InternalServiceNameE // unique creates an index
  config      Json
  credsConfig String?
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt

  @@id([id])
  @@unique([name])
}

//#endregion service

//#region log
model ServiceLog {
  id        String   @default(auto()) @map("_id") @db.ObjectId
  url       String
  request   Json
  response  Json
  profileId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([id])
}

model AppLog {
  id        String   @default(auto()) @map("_id") @db.ObjectId
  url       String
  request   Json
  response  Json
  profileId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([id])
}

//#endregion log
