import React from 'react';

interface StructuredDataProps {
  data: object | object[];
  id?: string;
}

const StructuredData: React.FC<StructuredDataProps> = ({ data, id }) => {
  // Ensure data is valid JSON-LD
  const jsonLdData = Array.isArray(data) ? data : [data];

  // Filter out any invalid data
  const validData = jsonLdData.filter(
    item => item && typeof item === 'object' && '@context' in item
  );

  if (validData.length === 0) {
    return null;
  }

  const finalData = validData.length === 1 ? validData[0] : validData;

  return (
    <script
      id={id}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(finalData, null, 0),
      }}
    />
  );
};

// Higher-order component for common schema types
export const WebsiteStructuredData: React.FC<{
  url?: string;
  name?: string;
  description?: string;
}> = ({
  url = 'https://app.navicater.com',
  name = 'Navicater',
  description = 'Maritime Knowledge Sharing Platform',
}) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name,
    url,
    description,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };

  return <StructuredData data={data} id="website-schema" />;
};

export const OrganizationStructuredData: React.FC<{
  url?: string;
  name?: string;
}> = ({ url = 'https://app.navicater.com', name = 'Navicater' }) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name,
    url,
    logo: {
      '@type': 'ImageObject',
      url: `${url}/android-chrome-512x512.png`,
      width: 512,
      height: 512,
    },
    description:
      'Maritime Knowledge Sharing Platform connecting professionals worldwide',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: 'English',
    },
  };

  return <StructuredData data={data} id="organization-schema" />;
};

export const BreadcrumbStructuredData: React.FC<{
  items: Array<{ name: string; url: string }>;
  baseUrl?: string;
}> = ({ items, baseUrl = 'https://app.navicater.com' }) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`,
    })),
  };

  return <StructuredData data={data} id="breadcrumb-schema" />;
};

export const FAQStructuredData: React.FC<{
  faqs: Array<{ question: string; answer: string }>;
}> = ({ faqs }) => {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={data} id="faq-schema" />;
};

export default StructuredData;
