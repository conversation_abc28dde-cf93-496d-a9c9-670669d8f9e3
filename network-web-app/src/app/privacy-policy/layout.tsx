import { generateMetadata } from '@/lib/seo';
import { BreadcrumbStructuredData } from '@/components/StructuredData';

export const metadata = generateMetadata({
  title: 'Privacy Policy - Navicater | Maritime Knowledge Sharing Platform',
  description:
    'Read our comprehensive privacy policy to understand how Navicater protects and handles your personal information on our maritime knowledge sharing platform.',
  canonical: '/privacy-policy',
  keywords: [
    'privacy policy',
    'data protection',
    'maritime platform',
    'navicater privacy',
    'personal information',
  ],
});

const PrivacyLayout = ({ children }: { children: React.ReactNode }) => {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Privacy Policy', url: '/privacy-policy' },
  ];

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {children}
    </>
  );
};

export default PrivacyLayout;
