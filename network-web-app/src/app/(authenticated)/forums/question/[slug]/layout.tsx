import { Metadata } from 'next';
import { Header } from '@/components';
import StructuredData, {
  BreadcrumbStructuredData,
} from '@/components/StructuredData';
import {
  fetchQuestionIdBySlug,
  fetchQuestionForPublic,
} from '@/networks/forum/questionDetail';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

function truncateDescription(
  description: string,
  maxLength: number = 160
): string {
  if (description.length <= maxLength) {
    return description;
  }

  const truncated = description.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...';
  }

  return truncated + '...';
}

function generateKeywords(title: string, topics: any[]): string[] {
  const baseKeywords = [
    'maritime',
    'seafarer',
    'shipping',
    'navigation',
    'marine',
    'forum',
    'question',
    'answer',
    'navicater',
  ];

  // Add topic keywords
  const topicKeywords = topics?.map(topic => topic.name.toLowerCase()) || [];

  // Extract keywords from title
  const titleWords = title
    .toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 3)
    .slice(0, 5);

  return [...baseKeywords, ...topicKeywords, ...titleWords].slice(0, 15);
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  // During build time, skip API calls to avoid errors
  if (
    process.env.NODE_ENV === 'production' &&
    !process.env.NEXT_PUBLIC_BASE_URL
  ) {
    return generateSEOMetadata({
      title: 'Maritime Forum Question - Navicater',
      description:
        'Join the maritime community discussion and share knowledge with professionals worldwide.',
      canonical: `/forums/question/${slug}`,
    });
  }

  try {
    const { id } = await fetchQuestionIdBySlug(slug);
    const question = await fetchQuestionForPublic(id);

    if (!question) {
      return generateSEOMetadata({
        title: 'Question Not Found - Navicater',
        description: 'The requested forum question could not be found.',
        noIndex: true,
      });
    }

    const title = `${question.title} - Navicater Forum`;
    const description = truncateDescription(question.description);
    const keywords = generateKeywords(question.title, question.topics || []);
    const authorName = question.profile?.name || 'Maritime Professional';
    const publishedTime = question.createdAt
      ? new Date(question.createdAt).toISOString()
      : undefined;
    const modifiedTime =
      question.isEdited && publishedTime ? publishedTime : publishedTime;

    return generateSEOMetadata({
      title,
      description,
      keywords,
      canonical: `/forums/question/${slug}`,
      openGraph: {
        title: question.title, // Use original title for OG
        description,
        type: 'article',
        author: authorName,
        publishedTime,
        modifiedTime,
        url: `/forums/question/${slug}`,
      },
      twitter: {
        card: 'summary_large_image',
        title: question.title, // Use original title for Twitter
        description,
        creator: `@${authorName.replace(/\s+/g, '').toLowerCase()}`,
      },
    });
  } catch (error) {
    console.error('Error generating metadata for question:', error);
    return generateSEOMetadata({
      title: 'Question Not Found - Navicater',
      description: 'The requested forum question could not be found.',
      noIndex: true,
    });
  }
}

export default async function QuestionDetailLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  let questionData = null;
  let questionStructuredData = null;

  // During build time, skip API calls to avoid errors
  if (
    process.env.NODE_ENV !== 'production' ||
    process.env.NEXT_PUBLIC_BASE_URL
  ) {
    try {
      const { id } = await fetchQuestionIdBySlug(slug);
      const question = await fetchQuestionForPublic(id);

      if (question) {
        questionData = question;

        // Generate QA Page structured data
        questionStructuredData = {
          '@context': 'https://schema.org',
          '@type': 'QAPage',
          mainEntity: {
            '@type': 'Question',
            name: question.title,
            text: question.description,
            dateCreated: question.createdAt,
            answerCount: question.answerCount || 0,
            author: {
              '@type': 'Person',
              name: question.profile?.name || 'Maritime Professional',
            },
            ...(question.answers &&
              question.answers.length > 0 && {
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: question.answers[0].text,
                  dateCreated: question.answers[0].createdAt,
                  author: {
                    '@type': 'Person',
                    name: question.answers[0].profile.name,
                  },
                },
              }),
          },
        };
      }
    } catch (error) {
      console.error('Error fetching question for layout:', error);
    }
  }

  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Forums', url: '/forums' },
    {
      name: questionData?.title || 'Question',
      url: `/forums/question/${slug}`,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {questionStructuredData && (
        <StructuredData data={questionStructuredData} id="qa-page-schema" />
      )}
      <Header />
      <main className="pt-16">{children}</main>
    </div>
  );
}
