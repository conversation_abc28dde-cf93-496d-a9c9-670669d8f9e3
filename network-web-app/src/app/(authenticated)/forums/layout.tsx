import { Metadata } from 'next';
import { generateMetadata } from '@/lib/seo';
import { BreadcrumbStructuredData } from '@/components/StructuredData';

export const metadata: Metadata = generateMetadata({
  title: 'Maritime Forums - Navicater | Connect with Maritime Professionals',
  description:
    'Join the largest maritime community forum. Ask questions, share knowledge, and connect with seafarers, marine engineers, and maritime professionals worldwide.',
  keywords: [
    'maritime forum',
    'seafarer community',
    'marine engineering',
    'shipping questions',
    'maritime professionals',
    'naval architecture',
    'port operations',
    'maritime safety',
    'vessel operations',
    'maritime knowledge',
    'shipping industry',
    'maritime careers',
    'marine technology',
    'maritime regulations',
    'navicater forum',
  ],
  canonical: '/forums',
  openGraph: {
    title: 'Maritime Forums - Connect with Maritime Professionals',
    description:
      'Join the largest maritime community forum. Ask questions, share knowledge, and connect with seafarers worldwide.',
    type: 'website',
    url: '/forums',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Maritime Forums - Connect with Maritime Professionals',
    description:
      'Join the largest maritime community forum. Ask questions, share knowledge, and connect with seafarers worldwide.',
  },
});

export default function ForumsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Forums', url: '/forums' },
  ];

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {children}
    </>
  );
}
