import { generateMetadata } from '@/lib/seo';
import { BreadcrumbStructuredData } from '@/components/StructuredData';

export const metadata = generateMetadata({
  title: 'Terms & Conditions - Navicater | Maritime Knowledge Sharing Platform',
  description:
    'Read our terms and conditions to understand the rules and guidelines for using Navicater, the premier maritime knowledge sharing platform.',
  canonical: '/terms',
  keywords: [
    'terms and conditions',
    'terms of service',
    'maritime platform terms',
    'navicater terms',
    'user agreement',
    'platform rules',
    'maritime community guidelines',
  ],
  openGraph: {
    title: 'Terms & Conditions - Navicater',
    description: 'Terms and conditions for using Navicater maritime platform',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Terms & Conditions - Navicater',
    description: 'Terms and conditions for using Navicater maritime platform',
  },
});

const TermsLayout = ({ children }: { children: React.ReactNode }) => {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Terms & Conditions', url: '/terms' },
  ];

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {children}
    </>
  );
};

export default TermsLayout;
