import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const title =
      searchParams.get('title') || 'Connect. Collaborate. Navigate.';
    const description =
      searchParams.get('description') ||
      'The ultimate maritime platform connecting professionals, sharing expertise, and building the future of maritime industry together.';
    const type = searchParams.get('type') || 'website';
    const author = searchParams.get('author') || '';
    const date = searchParams.get('date') || '';

    const interSemiBold = fetch(
      'https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2'
    ).then(res => res.arrayBuffer());

    const interRegular = fetch(
      'https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyYAZ9hiJ-Ek-_EeA.woff2'
    ).then(res => res.arrayBuffer());

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#ffffff',
            backgroundImage:
              'linear-gradient(135deg, #448600 0%, #2d5a00 100%)',
            position: 'relative',
          }}
        >
          {/* Background Pattern */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              opacity: 0.3,
            }}
          />

          {/* Main Content Container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '80px',
              maxWidth: '1000px',
              textAlign: 'center',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* Logo/Brand */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '40px',
              }}
            >
              <div
                style={{
                  width: '80px',
                  height: '80px',
                  backgroundColor: '#ffffff',
                  borderRadius: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '20px',
                  boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
                }}
              >
                <div
                  style={{
                    fontSize: '40px',
                    color: '#448600',
                    fontWeight: 'bold',
                  }}
                >
                  ⚓
                </div>
              </div>
              <div
                style={{
                  fontSize: '36px',
                  fontWeight: 'bold',
                  color: '#000000',
                  fontFamily: 'Inter',
                }}
              >
                NAVICATER
              </div>
            </div>

            <h1
              style={{
                fontSize: title.length > 50 ? '48px' : '64px',
                fontWeight: 'bold',
                color: '#ffffff',
                lineHeight: 1.2,
                marginBottom: '24px',
                fontFamily: 'Inter',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              {title}
            </h1>

            {/* Description */}
            {description && (
              <p
                style={{
                  fontSize: '24px',
                  color: '#e2e8f0',
                  lineHeight: 1.4,
                  marginBottom: '32px',
                  fontFamily: 'Inter',
                  maxWidth: '800px',
                }}
              >
                {description}
              </p>
            )}

            {/* Meta Information */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '24px',
                fontSize: '18px',
                color: '#cbd5e1',
                fontFamily: 'Inter',
              }}
            >
              {author && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ marginRight: '8px' }}>👤</span>
                  {author}
                </div>
              )}
              {date && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ marginRight: '8px' }}>📅</span>
                  {new Date(date).toLocaleDateString()}
                </div>
              )}
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '8px' }}>🌊</span>
                Maritime Platform
              </div>
            </div>

            {/* Type Badge */}
            {type !== 'website' && (
              <div
                style={{
                  position: 'absolute',
                  top: '40px',
                  right: '40px',
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: '#ffffff',
                  padding: '12px 24px',
                  borderRadius: '25px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  fontFamily: 'Inter',
                  backdropFilter: 'blur(10px)',
                }}
              >
                {type}
              </div>
            )}
          </div>

          {/* Bottom Wave */}
          <div
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '100px',
              background:
                'linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 100%)',
              clipPath: 'polygon(0 50%, 100% 80%, 100% 100%, 0% 100%)',
            }}
          />
        </div>
      ),
      {
        width: 1200,
        height: 630,
        fonts: [
          {
            name: 'Inter',
            data: await interSemiBold,
            style: 'normal',
            weight: 600,
          },
          {
            name: 'Inter',
            data: await interRegular,
            style: 'normal',
            weight: 400,
          },
        ],
      }
    );
  } catch (e: any) {
    console.log(`${e.message}`);
    return new Response(`Failed to generate the image`, {
      status: 500,
    });
  }
}
