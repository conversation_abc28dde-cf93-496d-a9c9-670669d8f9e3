import { Metadata } from 'next';

// Base URL for the application
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

// Default SEO configuration
const DEFAULT_SEO = {
  title: 'Navicater | Maritime Knowledge Sharing Platform',
  description:
    'Connect. Collaborate. Navigate. The ultimate maritime platform connecting professionals, sharing expertise, and building the future of maritime industry together.',
  keywords: [
    'maritime',
    'seafarer',
    'shipping',
    'navigation',
    'marine',
    'platform',
    'knowledge sharing',
    'navicater',
    'maritime professionals',
    'shipping industry',
  ],
  author: 'Navicater',
  siteName: 'Navicater',
  locale: 'en_US',
  type: 'website',
};

interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  noIndex?: boolean;
  noFollow?: boolean;
  openGraph?: {
    title?: string;
    description?: string;
    type?: 'website' | 'article' | 'profile';
    image?: string;
    imageAlt?: string;
    url?: string;
    siteName?: string;
    locale?: string;
    author?: string;
    publishedTime?: string;
    modifiedTime?: string;
  };
  twitter?: {
    card?: 'summary' | 'summary_large_image' | 'app' | 'player';
    site?: string;
    creator?: string;
    title?: string;
    description?: string;
    image?: string;
    imageAlt?: string;
  };
  jsonLd?: object;
}

export function generateMetadata(config: SEOConfig = {}): Metadata {
  const {
    title = DEFAULT_SEO.title,
    description = DEFAULT_SEO.description,
    keywords = DEFAULT_SEO.keywords,
    canonical,
    noIndex = false,
    noFollow = false,
    openGraph = {},
    twitter = {},
  } = config;

  // Generate OpenGraph image URL
  const ogImageUrl =
    openGraph.image ||
    generateOGImageUrl({
      title: openGraph.title || title,
      description: openGraph.description || description,
      type: openGraph.type || 'website',
      author: openGraph.author,
    });

  const canonicalUrl = canonical ? `${BASE_URL}${canonical}` : BASE_URL;

  const metadata: Metadata = {
    title,
    description,
    keywords: keywords.join(', '),
    authors: [{ name: DEFAULT_SEO.author, url: BASE_URL }],
    creator: DEFAULT_SEO.author,
    publisher: DEFAULT_SEO.author,
    metadataBase: new URL(BASE_URL),
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: !noIndex,
      follow: !noFollow,
      googleBot: {
        index: !noIndex,
        follow: !noFollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: openGraph.title || title,
      description: openGraph.description || description,
      url: openGraph.url || canonicalUrl,
      siteName: openGraph.siteName || DEFAULT_SEO.siteName,
      locale: openGraph.locale || DEFAULT_SEO.locale,
      type: openGraph.type || 'website',
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt:
            openGraph.imageAlt ||
            `${openGraph.title || title} - ${DEFAULT_SEO.siteName}`,
        },
      ],
      ...(openGraph.type === 'article' && {
        publishedTime: openGraph.publishedTime,
        modifiedTime: openGraph.modifiedTime,
        authors: [openGraph.author || DEFAULT_SEO.author],
      }),
    },
    twitter: {
      card: twitter.card || 'summary_large_image',
      site: twitter.site || '@navicater',
      creator: twitter.creator || '@navicater',
      title: twitter.title || openGraph.title || title,
      description: twitter.description || openGraph.description || description,
      images: [
        {
          url: twitter.image || ogImageUrl,
          alt:
            twitter.imageAlt ||
            openGraph.imageAlt ||
            `${title} - ${DEFAULT_SEO.siteName}`,
        },
      ],
    },
    other: {
      'theme-color': '#448600',
      'mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': 'Navicater',
      'application-name': 'Navicater',
      'msapplication-TileColor': '#448600',
      'msapplication-config': '/browserconfig.xml',
      // Performance and SEO optimizations
      'format-detection': 'telephone=no',
      referrer: 'origin-when-cross-origin',
      'color-scheme': 'light',
      // Security headers
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
    },
  };

  return metadata;
}

function generateOGImageUrl(params: {
  title: string;
  description: string;
  type: string;
  author?: string;
}): string {
  const searchParams = new URLSearchParams();
  searchParams.set('title', params.title);
  searchParams.set('description', params.description);
  searchParams.set('type', params.type);
  if (params.author) {
    searchParams.set('author', params.author);
  }

  return `${BASE_URL}/api/og?${searchParams.toString()}`;
}

interface StructuredDataConfig {
  title?: string;
  description?: string;
  url?: string;
  datePublished?: string;
  dateModified?: string;
  author?: string;
  image?: string;
}

export function generateStructuredData(
  type: 'website' | 'organization' | 'article' | 'qapage',
  config: StructuredDataConfig = {}
): object {
  const baseData = {
    '@context': 'https://schema.org',
    url: config.url || BASE_URL,
    name: config.title || DEFAULT_SEO.title,
    description: config.description || DEFAULT_SEO.description,
  };

  switch (type) {
    case 'website':
      return {
        ...baseData,
        '@type': 'WebSite',
        publisher: {
          '@type': 'Organization',
          name: DEFAULT_SEO.siteName,
          url: BASE_URL,
          logo: {
            '@type': 'ImageObject',
            url: `${BASE_URL}/android-chrome-512x512.png`,
            width: 512,
            height: 512,
          },
        },
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${BASE_URL}/search?q={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
      };

    case 'organization':
      return {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: DEFAULT_SEO.siteName,
        url: BASE_URL,
        logo: {
          '@type': 'ImageObject',
          url: `${BASE_URL}/android-chrome-512x512.png`,
          width: 512,
          height: 512,
        },
        description: DEFAULT_SEO.description,
        sameAs: [
          // Add social media URLs here when available
        ],
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          availableLanguage: 'English',
        },
      };

    case 'article':
      return {
        ...baseData,
        '@type': 'Article',
        headline: config.title,
        datePublished: config.datePublished,
        dateModified: config.dateModified || config.datePublished,
        author: {
          '@type': 'Person',
          name: config.author || DEFAULT_SEO.author,
        },
        publisher: {
          '@type': 'Organization',
          name: DEFAULT_SEO.siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${BASE_URL}/android-chrome-512x512.png`,
            width: 512,
            height: 512,
          },
        },
        image: config.image || `${BASE_URL}/android-chrome-512x512.png`,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': config.url || BASE_URL,
        },
      };

    case 'qapage':
      return {
        ...baseData,
        '@type': 'QAPage',
        mainEntity: {
          '@type': 'Question',
          name: config.title,
          text: config.description,
          dateCreated: config.datePublished,
          author: {
            '@type': 'Person',
            name: config.author || 'Maritime Professional',
          },
        },
      };

    default:
      return baseData;
  }
}
