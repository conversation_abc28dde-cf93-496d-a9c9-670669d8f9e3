import { apiCall } from '@/lib/api';

export interface PrivacyPolicyResponse {
  id: string;
  content: string;
}

export interface FetchPrivacyPolicyParams {
  type: 'SIGNUP_PRIVACY_POLICY' | 'TERMS_OF_USE';
}

export const fetchPrivacyPolicy = async (
  params: FetchPrivacyPolicyParams
): Promise<PrivacyPolicyResponse> => {
  const result = await apiCall<undefined, PrivacyPolicyResponse>(
    '/backend/api/v1/privacy-policy',
    'GET',
    {
      isAuth: false,
      query: params,
    }
  );

  return result;
};

export const fetchTermsOfUse = async (): Promise<PrivacyPolicyResponse> => {
  return fetchPrivacyPolicy({ type: 'TERMS_OF_USE' });
};

export const fetchPrivacyPolicyContent =
  async (): Promise<PrivacyPolicyResponse> => {
    return fetchPrivacyPolicy({ type: 'SIGNUP_PRIVACY_POLICY' });
  };
