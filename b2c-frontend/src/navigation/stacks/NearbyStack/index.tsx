import { createStackNavigator } from '@react-navigation/stack';
import type { NearbyStackParamListI } from '@/src/navigation/types';
import { screens } from './screen';

const NearbyStack = createStackNavigator<NearbyStackParamListI>();

const NearbyStackNavigator = () => (
  <NearbyStack.Navigator
    screenOptions={{
      headerShown: false,
      animation: 'slide_from_right',
      cardStyle: { backgroundColor: 'white' },
      cardOverlayEnabled: false,
      cardShadowEnabled: false,
    }}
    initialRouteName="Nearby"
  >
    {screens.map(({ name, component }) => (
      <NearbyStack.Screen key={name} name={name} component={component} />
    ))}
  </NearbyStack.Navigator>
);

export default NearbyStackNavigator;
