import { Text, View, ScrollView, Pressable } from 'react-native';
import { createDrawerNavigator, DrawerItemList } from '@react-navigation/drawer';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import BottomTabNavigator from '@/src/navigation/tabs/TabNavigation';

const Drawer = createDrawerNavigator();

const CustomDrawerContent = (props: any) => {
  const currentUser = useSelector(selectCurrentUser);
  const userDescription =
    currentUser?.description ||
    'Building @ Navicater | Python | Typescript | LLM | Go | Nextjs | AWS | Ex-YC SDE | Open Source';
  const userDesignation = currentUser?.designation?.name || 'Software Engineer';
  const userOrganisation = currentUser?.organisation?.name || 'Navicater';
  console.log(currentUser?.country?.name);

  const userLocation = currentUser?.country?.name || 'Prayagraj, Uttar Pradesh, India';
  const profileViewers = 84;
  const postImpressions = 313;

  return (
    <SafeArea>
      <ScrollView style={{ flex: 1, backgroundColor: '#ffffff' }}>
        <View
          style={{
            padding: 20,
            alignItems: 'flex-start',
            borderBottomWidth: 1,
            borderBottomColor: '#f0f0f0',
            marginBottom: 10,
          }}
        >
          <UserAvatar
            avatarUri={currentUser?.avatar}
            name={currentUser?.fullName}
            width={60}
            height={60}
          />
          <Text style={{ fontSize: 20, fontWeight: 'bold', marginTop: 10, color: '#000000' }}>
            {currentUser?.fullName || 'Guest User'}
          </Text>
          <Text style={{ fontSize: 14, color: '#333333', marginTop: 4, lineHeight: 20 }}>
            {userDescription}
          </Text>
          <Text style={{ fontSize: 14, color: '#666666', marginTop: 2 }}>
            {userDesignation} at {userOrganisation}
          </Text>
          <Text style={{ fontSize: 14, color: '#666666', marginTop: 2 }}>{userLocation}</Text>
        </View>

        <View style={{ paddingHorizontal: 20, paddingVertical: 10 }}>
          <Pressable style={{ flexDirection: 'row', alignItems: 'center', paddingVertical: 5 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#006400', marginRight: 5 }}>
              {profileViewers}
            </Text>
            <Text style={{ fontSize: 14, color: '#666666' }}>profile viewers</Text>
          </Pressable>
          <Pressable style={{ flexDirection: 'row', alignItems: 'center', paddingVertical: 5 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#006400', marginRight: 5 }}>
              {postImpressions}
            </Text>
            <Text style={{ fontSize: 14, color: '#666666' }}>post impressions</Text>
          </Pressable>
        </View>

        <View style={{ height: 1, backgroundColor: '#f0f0f0', marginVertical: 10 }} />

        <DrawerItemList {...props} />

        <View style={{ height: 1, backgroundColor: '#f0f0f0', marginVertical: 10 }} />

        <View style={{ paddingHorizontal: 20, paddingVertical: 10 }}>
          <Pressable style={{ paddingVertical: 12 }}>
            <Text style={{ fontSize: 16, color: '#000000' }}>Referral</Text>
          </Pressable>
          <Pressable style={{ paddingVertical: 12 }}>
            <Text style={{ fontSize: 16, color: '#000000' }}>Logout</Text>
          </Pressable>
        </View>
      </ScrollView>
    </SafeArea>
  );
};

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '80%',
          backgroundColor: '#ffffff',
        },
        drawerLabelStyle: { fontSize: 16, color: '#000000' },
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerActiveTintColor: '#000000',
        drawerInactiveTintColor: '#000000',
      }}
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen name="MainTabs" component={BottomTabNavigator} options={{ title: 'Home' }} />
      <Drawer.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={{ title: 'Settings' }}
      />
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
