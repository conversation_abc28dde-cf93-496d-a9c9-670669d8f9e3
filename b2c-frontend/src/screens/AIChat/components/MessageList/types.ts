import type { RefObject } from 'react';
import type { ScrollView } from 'react-native';
import type { Message } from '@/src/redux/slices/aichat/types';

export interface MessageGroup {
  dateLabel: string;
  messages: Message[];
}

export interface MessageListProps {
  messages: Message[];
  loading: boolean;
  scrollRef: RefObject<ScrollView | null>;
  messageGroups: MessageGroup[];
  streamedText?: string;
  currentAiMessageId?: number | null;
}
