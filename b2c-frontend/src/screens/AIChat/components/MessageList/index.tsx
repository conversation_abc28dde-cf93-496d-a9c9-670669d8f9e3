import type React from 'react';
import { ScrollView, View } from 'react-native';
import { DateSeparator } from '../DateSeparator';
import { MessageBubble } from '../MessageBubble';
import type { MessageListProps } from './types';

export const MessageList: React.FC<MessageListProps> = ({
  loading,
  scrollRef,
  messageGroups,
  streamedText,
  currentAiMessageId,
}) => {
  return (
    <ScrollView
      ref={scrollRef}
      className="flex-1 px-4"
      contentContainerStyle={{ paddingVertical: 20 }}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
      onContentSizeChange={() => scrollRef.current?.scrollToEnd({ animated: true })}
    >
      {messageGroups.map(({ dateLabel, messages: groupMessages }) => (
        <View key={dateLabel}>
          <DateSeparator label={dateLabel} />
          {groupMessages.map((msg) => (
            <MessageBubble
              key={msg.id}
              message={msg}
              isLoading={loading && !msg.text && msg.from === 'ai'}
              streamedText={streamedText}
              currentAiMessageId={currentAiMessageId}
            />
          ))}
        </View>
      ))}
    </ScrollView>
  );
};
