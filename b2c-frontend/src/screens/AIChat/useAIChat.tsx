import { useRef, useState, useEffect } from 'react';
import type { ScrollView } from 'react-native';
import Config from 'react-native-config';
import EventSource from 'react-native-sse';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addMessage,
  updateMessage,
  clearChat,
  removeOldMessages,
} from '@/src/redux/slices/aichat/aiChatSlice';
import type { Message } from '@/src/redux/slices/aichat/types';
import type { RootState } from '@/src/redux/store';
import AiBot from '@/src/assets/images/others/aibot.png';
import useStorage from '@/src/hooks/storage';

const AI_USER = {
  id: 'ai',
  name: 'Navicater AI',
  avatar: AiBot,
};

const TWENTY_DAYS_MS = 20 * 24 * 60 * 60 * 1000;
const AI_URL = Config.AI_URL;

export const useAIChat = () => {
  const dispatch = useDispatch();
  const messages = useSelector((state: RootState) => state.aichat.messages);
  const currentUser = useSelector(selectCurrentUser);
  const { getStorage } = useStorage();

  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(false);
  const [showRateLimitModal, setShowRateLimitModal] = useState(false);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [aiText, setAiText] = useState('');
  const [streamedText, setStreamedText] = useState('');
  const [currentAiMessageId, setCurrentAiMessageId] = useState<number | null>(null);

  const scrollRef = useRef<ScrollView>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!aiText || loading || !currentAiMessageId) {
      return;
    }

    let currentIndex = 0;
    setStreamedText('');

    const streamText = () => {
      if (currentIndex < aiText.length) {
        currentIndex = Math.min(currentIndex + 4, aiText.length);
        const currentStreamedText = aiText.slice(0, currentIndex);
        setStreamedText(currentStreamedText);
        dispatch(updateMessage({ id: currentAiMessageId, text: currentStreamedText }));
        scrollToEnd();

        if (currentIndex < aiText.length) {
          streamingTimeoutRef.current = setTimeout(streamText, 50);
        } else {
          setStreamedText('');
          setAiText('');
          setCurrentAiMessageId(null);
        }
      }
    };

    streamText();

    return () => {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, [aiText, loading, currentAiMessageId, dispatch]);

  useEffect(() => {
    dispatch(removeOldMessages(TWENTY_DAYS_MS));
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  const scrollToEnd = () => {
    setTimeout(() => {
      scrollRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const streamAIResponse = async (query: string) => {
    setLoading(true);
    setAiText('');
    setStreamedText('');

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }

    const aiMsgId = Date.now() + 1;
    setCurrentAiMessageId(aiMsgId);

    const aiMsg: Message = {
      id: aiMsgId,
      text: '',
      from: 'ai',
      user: AI_USER,
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(aiMsg));
    scrollToEnd();

    const url = `${AI_URL}/query/general?query=${encodeURIComponent(query)}&device=${await getStorage('deviceToken')}`;
    const es = new EventSource(url);
    eventSourceRef.current = es;

    let accumulatedText = '';

    es.addEventListener('message', (event) => {
      try {
        const parsed = JSON.parse(event.data ?? '');
        const text = parsed.text;

        if (text === '[DONE]') {
          es.close();
          eventSourceRef.current = null;
          setLoading(false);
          if (accumulatedText.trim()) {
            setAiText(accumulatedText);
          }
          return;
        }

        accumulatedText += text;
      } catch (err) {
        console.error('❌ Failed to parse SSE message:', event.data);
      }
    });

    es.addEventListener('error', () => {
      es.close();
      eventSourceRef.current = null;
      setLoading(false);
      setShowRateLimitModal(true);
      dispatch(
        updateMessage({ id: aiMsgId, text: accumulatedText || 'Sorry, there was an error.' }),
      );
      setCurrentAiMessageId(null);
      scrollToEnd();
    });
  };

  const handleSend = () => {
    if (!messageText.trim() || loading) return;

    const userMsg: Message = {
      id: Date.now(),
      text: messageText.trim(),
      from: 'me',
      user: {
        id: 'me',
        name: 'You',
        avatar: { uri: currentUser?.avatar! },
      },
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMsg));
    const query = messageText.trim();
    setMessageText('');
    scrollToEnd();
    streamAIResponse(query);
  };

  const handleClearChat = () => {
    dispatch(clearChat());
    setIsBottomSheetOpen(false);
  };

  return {
    messages,
    messageText,
    setMessageText,
    loading,
    isBottomSheetOpen,
    setIsBottomSheetOpen,
    scrollRef,
    handleSend,
    handleClearChat,
    currentUser,
    showRateLimitModal,
    setShowRateLimitModal,
    streamedText,
    currentAiMessageId,
  };
};
