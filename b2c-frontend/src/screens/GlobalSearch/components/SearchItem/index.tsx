import type React from 'react';
import { Text, View, Pressable, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import UserAvatar from '@/src/components/UserAvatar';
import UserPost from '@/src/components/UserPost';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addReactionOptimistc,
  deletePostOptimistic,
  removeReactionOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import type { RootState } from '@/src/redux/store';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Company from '@/src/assets/svgs/Company';
import Institute from '@/src/assets/svgs/Institute';
import Ship from '@/src/assets/svgs/Ship';
import { deletePostAPI } from '@/src/networks/content/post';
import { deleteReactionAPI, upsertReactionAPI } from '@/src/networks/content/reaction';
import type { PostExternalClientI } from '@/src/networks/content/types';
import { SearchResultItemI } from '../SearchResults/types';
import type { SearchItemProps } from './types';

const SearchItem: React.FC<SearchItemProps> = ({
  item,
  activeTab,
  onItemPress,
  onError,
  postIndex,
  parentScrollRef,
  isPopularMode = false,
}) => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch();

  const getConnectionDegree = (priority: number) => {
    switch (priority) {
      case 1:
        return '1st';
      case 2:
        return '2nd';
      case 3:
        return '3rd';
      default:
        return '';
    }
  };

  const handleLikePost = async (
    postId: string,
    isCurrentlyLiked: boolean,
    reactionType = 'LIKE',
  ) => {
    if (!currentUser) return;

    try {
      if (isCurrentlyLiked) {
        dispatch(removeReactionOptimistic({ postId }));
        await deleteReactionAPI({ postId });
      } else {
        dispatch(addReactionOptimistc({ postId }));
        await upsertReactionAPI({ postId, reactionType });
      }
    } catch (error) {
      if (isCurrentlyLiked) {
        dispatch(addReactionOptimistc({ postId }));
      } else {
        dispatch(removeReactionOptimistic({ postId }));
      }
      const errorMessage = `Failed to handle like post: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleDeletePost = async (post: PostExternalClientI) => {
    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(post.id);
    } catch (error) {
      dispatch(revertDeletePostOptimistic({ postId: post.id }));
      const errorMessage = `Failed to delete post: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handlePostComment = (post: PostExternalClientI) => {
    navigation.navigate('HomeStack', {
      screen: 'Comment',
      params: { postId: post.id, type: 'USER_POST' },
    });
  };

  const handlePostEdit = (post: PostExternalClientI) => {
    navigation.navigate('CreateStack', {
      screen: 'CreateContent',
      params: { editing: true, postId: post.id, type: 'USER_POST' },
    });
  };

  const handleLikeCountPress = (postId: string) => {
    navigation.navigate('HomeStack', {
      screen: 'Likes',
      params: { postId: postId, type: 'USER_POST' },
    });
  };

  const handleUserProfileNavigation = (profileId: string) => {
    navigation.navigate('HomeStack', {
      screen: 'OtherUserProfile',
      params: { profileId: profileId, fromTabPress: false },
    });
  };

  const getContainerClassName = (isPort = false) => {
    let classes = 'flex-row gap-4 p-4';
    if (isPopularMode) {
      classes += ' mb-2 rounded-lg shadow-sm bg-white';
    } else {
      classes += ' border-b border-gray-200';
    }
    classes += isPort ? ' items-start' : ' items-center';
    return classes;
  };

  const postItemProp = item as PostExternalClientI;
  const postFromRedux = useSelector((state: RootState) => {
    const searchPost = Object.values(state.content.searchPosts)
      .flat()
      .find((p) => p.id === postItemProp.id);
    const popularPost = state.content.popularPosts?.find((p) => p.id === postItemProp.id);
    const regularPost = state.content.posts.find((p) => p.id === postItemProp.id);

    return searchPost || popularPost || regularPost;
  });

  const currentPost = postFromRedux || postItemProp;

  switch (activeTab) {
    case 'people':
      const peopleItem = item as SearchResultItemI;
      return (
        <Pressable
          onPress={() =>
            isPopularMode
              ? handleUserProfileNavigation(peopleItem.profileId!)
              : onItemPress
                ? onItemPress(peopleItem, activeTab)
                : handleUserProfileNavigation(peopleItem.profileId!)
          }
          className={getContainerClassName()}
        >
          <UserAvatar avatarUri={peopleItem.avatar || null} name={peopleItem.name} />
          <View className="flex-1">
            <View className="flex-row items-center justify-between">
              <Text
                className="text-base font-medium text-gray-800"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {peopleItem.name}
              </Text>
              {currentUser.profileId !== peopleItem.profileId && peopleItem.priority && (
                <View className="px-2 py-1 rounded-full bg-gray-100 ml-2">
                  <Text className="text-xs font-medium text-gray-600">
                    {getConnectionDegree(peopleItem.priority)}
                  </Text>
                </View>
              )}
            </View>
            {peopleItem.designation && (
              <Text
                className="text-gray-500 mt-1 font-light"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {peopleItem.designation.name}
                {peopleItem.entity ? ' at ' + peopleItem.entity.name : ''}
              </Text>
            )}
          </View>
        </Pressable>
      );

    case 'organization':
      const organizationItem = item as SearchResultItemI;
      return (
        <Pressable
          onPress={() => (onItemPress ? onItemPress(organizationItem, activeTab) : null)}
          className={getContainerClassName()}
        >
          <View className="w-10 h-10 rounded-full bg-green-50 items-center justify-center">
            <Company />
          </View>
          <View className="flex-1">
            <Text
              className="text-base font-medium text-gray-800"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {organizationItem.name}
            </Text>
            <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
              Organization
            </Text>
          </View>
        </Pressable>
      );

    case 'ship':
      const shipItem = item as SearchResultItemI;
      return (
        <Pressable
          onPress={() => {
            if (isPopularMode) {
              navigation.navigate('HomeStack', {
                screen: 'ShipProfile',
                params: { dataType: shipItem.dataType || '', imo: shipItem.imo || '' },
              });
            } else {
              if (onItemPress) {
                onItemPress(shipItem, activeTab);
              } else {
                navigation.navigate('HomeStack', {
                  screen: 'ShipProfile',
                  params: { dataType: shipItem.dataType || '', imo: shipItem.imo || '' },
                });
              }
            }
          }}
          className={getContainerClassName()}
        >
          <View className="w-10 h-10 rounded-full bg-blue-50 items-center justify-center overflow-hidden">
            {shipItem.imageUrl ? (
              <Image
                source={{ uri: shipItem.imageUrl }}
                style={{ width: 40, height: 40, borderRadius: 20 }}
                resizeMode="cover"
              />
            ) : (
              <Ship />
            )}
          </View>
          <View className="flex-1">
            <Text
              className="text-base font-medium text-gray-800"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {shipItem.matchedName}
            </Text>
            <View>
              <Text
                className="text-gray-500 text-sm mt-1 font-light"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                IMO: {shipItem.imo}
              </Text>
            </View>
          </View>
        </Pressable>
      );

    case 'port':
      const portItem = item as SearchResultItemI;
      return (
        <Pressable
          onPress={() => {
            if (isPopularMode) {
              navigation.navigate('HomeStack', {
                screen: 'PortProfile',
                params: { dataType: portItem.dataType || '', unLocode: portItem.unLocode || '' },
              });
            } else {
              if (onItemPress) {
                onItemPress(portItem, activeTab);
              } else {
                navigation.navigate('HomeStack', {
                  screen: 'PortProfile',
                  params: { dataType: portItem.dataType || '', unLocode: portItem.unLocode || '' },
                });
              }
            }
          }}
          className={getContainerClassName(true)}
        >
          <View className="w-12 h-12 rounded-full bg-amber-50 items-center justify-center overflow-hidden">
            {portItem.imageUrl ? (
              <Image
                source={{ uri: portItem.imageUrl }}
                style={{ width: 48, height: 48, borderRadius: 24 }}
                resizeMode="cover"
              />
            ) : (
              <Ship />
            )}
          </View>
          <View className="flex-1 justify-center">
            <Text
              className="text-base font-medium text-gray-800"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {portItem.name}
            </Text>
            <View className="flex-row items-center justify-between mt-0.5">
              <Text className="text-sm text-gray-500" numberOfLines={1} ellipsizeMode="tail">
                {portItem.city?.name}
                {portItem.country ? `, ${portItem.country.name}` : ''}
              </Text>
              <Text className="text-sm text-gray-500 ml-2">{portItem.unLocode}</Text>
            </View>
          </View>
        </Pressable>
      );

    case 'institution':
      const institutionItem = item as SearchResultItemI;
      return (
        <Pressable
          onPress={() => (onItemPress ? onItemPress(institutionItem, activeTab) : null)}
          className={getContainerClassName()}
        >
          <View className="w-10 h-10 rounded-full bg-green-50 items-center justify-center">
            <Institute />
          </View>
          <View className="flex-1">
            <Text
              className="text-base font-medium text-gray-800"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {institutionItem.name}
            </Text>
            <Text className="text-gray-500 mt-1 font-light" numberOfLines={1} ellipsizeMode="tail">
              Institution
            </Text>
          </View>
        </Pressable>
      );

    case 'post':
      if (!currentPost?.Profile) return null;
      const isOwnPost = currentUser?.profileId === currentPost.Profile.id;
      return (
        <View className={isPopularMode ? 'mb-2 rounded-lg shadow-sm bg-white' : ''}>
          <UserPost
            post={currentPost}
            onLikeCountPress={() => handleLikeCountPress(currentPost.id)}
            onCommentPress={() => handlePostComment(currentPost)}
            onDeletePress={() => handleDeletePost(currentPost)}
            onEditPress={() => handlePostEdit(currentPost)}
            onLikePress={() => handleLikePost(currentPost.id, currentPost.isLiked)}
            type="USER_POST"
            isOwnPost={isOwnPost}
            parentScrollRef={parentScrollRef}
            postIndex={postIndex || 0}
          />
        </View>
      );

    default:
      return null;
  }
};

export default SearchItem;
