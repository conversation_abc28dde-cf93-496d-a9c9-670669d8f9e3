import type React from 'react';
import type { GlobalSearchCategory } from '@/src/utilities/search/types';
import { PostExternalClientI } from '@/src/networks/content/types';
import { SearchResultItemI } from '../SearchResults/types';

export interface SearchItemProps {
  item: SearchResultItemI | PostExternalClientI;
  activeTab: GlobalSearchCategory;
  onItemPress?: (
    item: SearchResultItemI | PostExternalClientI,
    category: GlobalSearchCategory,
  ) => Promise<void>;
  onError?: (error: Error) => void;
  postIndex?: number;
  parentScrollRef?: React.RefObject<any>;
  isPopularMode?: boolean;
}
