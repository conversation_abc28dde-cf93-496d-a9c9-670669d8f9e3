import type React from 'react';
import { View, Text, FlatList } from 'react-native';
import SearchItem from '../SearchItem';
import type { PopularResultsProps } from './types';

const PopularResults: React.FC<PopularResultsProps> = ({ suggestions, activeTab }) => {
  const getIcon = () => {
    switch (activeTab) {
      case 'people':
        return '👥';
      case 'post':
        return '📝';
      case 'ship':
        return '🚢';
      case 'port':
        return '⚓';
      default:
        return '⭐';
    }
  };

  return (
    <View className="flex-1">
      <View className="px-4 pt-4 pb-3">
        <View className="flex-row items-center">
          <Text className="text-xl mr-2">{getIcon()}</Text>
          <Text className="text-gray-800 text-base font-semibold">Suggestions</Text>
        </View>
      </View>
      <FlatList
        data={suggestions}
        contentContainerClassName="pb-8"
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <SearchItem item={item} activeTab={activeTab} isPopularMode={true} />
        )}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingTop: 8,
        }}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default PopularResults;
