import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import Checkbox from '@/src/components/Checkbox';
import PrivacyModal from '@/src/components/PrivacyModal';
import TermsModal from '@/src/components/TermsModal';
import { AcceptTermsPropsI } from './types';

const AcceptTerms: React.FC<AcceptTermsPropsI> = ({ onTermsAccepted, showError = false }) => {
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  const handleTermsChange = (value: boolean) => {
    setAcceptedTerms(value);
    onTermsAccepted(value);
  };

  const handlePrivacyModal = () => {
    setShowPrivacyModal(!showPrivacyModal);
  };

  const handleTermsModal = () => {
    setShowTermsModal(!showTermsModal);
  };

  return (
    <>
      <View className="mb-4">
        <Checkbox
          label=""
          onValueChange={handleTermsChange}
          checked={acceptedTerms}
          className="z-10"
        />
        <View className="flex-row flex-wrap items-center ml-8 -mt-6">
          <Text className="text-sm text-gray-700">I agree to the Navicater </Text>
          <Pressable onPress={handlePrivacyModal}>
            <Text className="text-sm text-[#448600] font-medium underline">Privacy Policy</Text>
          </Pressable>
          <Text className="text-sm text-gray-700"> and </Text>
          <Pressable onPress={handleTermsModal}>
            <Text className="text-sm text-[#448600] font-medium underline">Terms & Conditions</Text>
          </Pressable>
        </View>
        {showError && !acceptedTerms && (
          <Text className="text-red-500 text-sm mt-1 ml-8">
            Please accept the terms and conditions to proceed
          </Text>
        )}
      </View>
      <PrivacyModal isVisible={showPrivacyModal} onClose={handlePrivacyModal} />
      <TermsModal isVisible={showTermsModal} onClose={handleTermsModal} />
    </>
  );
};

export default AcceptTerms;
