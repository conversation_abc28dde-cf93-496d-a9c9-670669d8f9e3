/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Pressable, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AddItem from '@/src/assets/svgs/AddItem';
import Explore from '@/src/assets/svgs/Explore';
import ForumSearch from '@/src/assets/svgs/ForumSearch';
import MyCommunities from '@/src/assets/svgs/MyCommunities';

const NavigationBar = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const handleAdd = () => {
    navigation.navigate('CommunityQuestion', { id: '' });
  };
  return (
    <View className="">
      <View className="flex-row justify-between items-center px-4 mb-2 bg-white  border-gray-200 rounded-lg">
        <Pressable className="items-center justify-center">
          <ForumSearch height={6} width={6} />
          {/* <Text className="text-xs text-black">Forum</Text> */}
        </Pressable>
        <Pressable className="items-center justify-center">
          <Explore height={6} width={6} />
          {/* <Text className="text-xs text-black">Explore</Text> */}
        </Pressable>
        <Pressable className="items-center justify-center">
          <MyCommunities height={6} width={6} />
          {/* <Text className="text-xs text-black">Comm</Text> */}
        </Pressable>
        <Pressable onPress={handleAdd} className="items-center justify-center">
          <AddItem height={6} width={6} />
          {/* <Text className="text-xs text-black">Create</Text> */}
        </Pressable>
      </View>
    </View>
  );
};

export default NavigationBar;
