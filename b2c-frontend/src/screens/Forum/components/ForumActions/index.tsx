import React from 'react';
import { View, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ExploreContained from '@/src/assets/svgs/ExploreContained';
import Search from '@/src/assets/svgs/Search';
import UsersThree from '@/src/assets/svgs/UsersThree';

const defaultActions = [
  {
    id: 'search',
    icon: <Search color="#448600" width={3} height={3} />,
    route: 'ForumSearch',
  },
  // {
  //   id: 'explore',
  //   icon: <ExploreContained color="#448600" width={3} height={3} />,
  //   route: 'Explore',
  // },
  // {
  //   id: 'my-community',
  //   icon: <UsersThree color="#448600" width={4} height={4} />,
  //   route: 'MyCommunities',
  // },
];

const ForumActions = ({ actions = [] }: { actions?: typeof defaultActions }) => {
  const navigation = useNavigation();

  const actionsToRender = actions.length > 0 ? actions : defaultActions;

  return (
    <View className="flex-row space-x-3 items-center justify-between">
      {actionsToRender.map((action, index) => (
        <Pressable
          key={action.id || index}
          onPress={() => {
            if (action.route) navigation.navigate(action.route as never);
          }}
          className="w-14 h-14 py-3 px-6 mx-6 border border-gray-200 rounded-xl items-center justify-center shadow-sm active:scale-95"
          android_ripple={{ color: '#ccc' }}
        >
          <View className="w-full h-full items-center justify-center">{action.icon}</View>
        </Pressable>
      ))}
    </View>
  );
};

export default ForumActions;
