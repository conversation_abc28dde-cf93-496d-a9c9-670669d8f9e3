import React, { act, useEffect, useRef, useState } from 'react';
import { View, Text } from 'react-native';

type TimerProps = {
  endTime: number;
};

const pad = (n: number) => n.toString().padStart(2, '0');

const getTimeLeft = (endTime: number) => {
  const now = Date.now();
  let diff = Math.max(0, endTime - now);
  const hours = Math.floor(diff / (1000 * 60 * 60));
  diff -= hours * 1000 * 60 * 60;
  const minutes = Math.floor(diff / (1000 * 60));
  diff -= minutes * 1000 * 60;
  const seconds = Math.floor(diff / 1000);
  return { hours, minutes, seconds };
};

const Timer: React.FC<TimerProps> = ({ endTime }) => {
  const [time, setTime] = useState(() => getTimeLeft(endTime));
  const frame = useRef<number>(0);

  useEffect(() => {
    let mounted = true;
    const update = () => {
      if (!mounted) return;
      setTime(getTimeLeft(endTime));
      if (endTime > Date.now()) {
        frame.current = requestAnimationFrame(update);
      }
    };
    frame.current = requestAnimationFrame(update);
    return () => {
      mounted = false;
      if (frame.current) cancelAnimationFrame(frame.current);
    };
  }, [endTime]);

  return (
    <View className="flex-row items-center justify-center py-2">
      <Text className="text-sm font-normal text-black">
        {pad(time.hours)}h {pad(time.minutes)}m
      </Text>
    </View>
  );
};

export default Timer;
