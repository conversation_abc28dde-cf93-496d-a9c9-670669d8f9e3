/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectForumAnswers } from '@/src/redux/selectors/forum';
import { selectForumQuestions } from '@/src/redux/selectors/question';
import {
  addAnswerVoteOptimistic,
  removeAnswerVoteOptimistic,
  switchAnswerVoteOptimistic,
} from '@/src/redux/slices/forum/forumSlice';
import {
  addQuestionVoteOptimistic,
  removeQuestionVoteOptimistic,
  switchQuestionVoteOptimistic,
  updateQuestionLiveStatus,
} from '@/src/redux/slices/question/questionSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { VoteType } from '@/src/networks/answerVote/types';
import {
  createQuestionVoteAPI,
  deleteQuestionVoteAPI,
  createAnswerVoteAPI,
  deleteAnswerVoteAPI,
} from '@/src/networks/forum/vote';

export const useQuestionVoting = (questionId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const forumQuestions = useSelector(selectForumQuestions);
  const [isLoading, setIsLoading] = useState(false);

  const question = forumQuestions.find((q) => q.id === questionId);
  const currentVote = question?.userVote || null;

  const handleVote = async (type: VoteType) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      if (currentVote === type) {
        dispatch(removeQuestionVoteOptimistic({ questionId, type }));

        await deleteQuestionVoteAPI({ questionId });
      } else if (currentVote && currentVote !== type) {
        dispatch(switchQuestionVoteOptimistic({ questionId, fromType: currentVote, toType: type }));

        await deleteQuestionVoteAPI({ questionId });
        await createQuestionVoteAPI({ questionId, type });
      } else {
        dispatch(addQuestionVoteOptimistic({ questionId, type }));
        await createQuestionVoteAPI({ questionId, type });
      }
    } catch (error) {
      if (currentVote === type) {
        dispatch(addQuestionVoteOptimistic({ questionId, type }));
      } else if (currentVote && currentVote !== type) {
        dispatch(switchQuestionVoteOptimistic({ questionId, fromType: type, toType: currentVote }));
      } else {
        dispatch(removeQuestionVoteOptimistic({ questionId, type }));
      }

      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpvote = () => handleVote('UPVOTE');
  const handleDownvote = () => handleVote('DOWNVOTE');

  return {
    currentVote,
    isLoading,
    handleUpvote,
    handleDownvote,
    isUpvoted: currentVote === 'UPVOTE',
    isDownvoted: currentVote === 'DOWNVOTE',
    upVotes: question?.upvoteCount || 0,
    downVotes: question?.downvoteCount || 0,
  };
};

export const useAnswerVoting = (answerId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const forumAnswers = useSelector(selectForumAnswers);
  const [isLoading, setIsLoading] = useState(false);

  const answer = forumAnswers.find((a) => a.id === answerId);
  const currentVote = answer?.userVote || null;

  const handleVote = async (type: VoteType) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      if (currentVote === type) {
        dispatch(removeAnswerVoteOptimistic({ answerId, type }));

        await deleteAnswerVoteAPI({ answerId });
      } else if (currentVote && currentVote !== type) {
        dispatch(switchAnswerVoteOptimistic({ answerId, fromType: currentVote, toType: type }));
        await deleteAnswerVoteAPI({ answerId });
        await createAnswerVoteAPI({ answerId, type });
      } else {
        dispatch(addAnswerVoteOptimistic({ answerId, type }));
        await createAnswerVoteAPI({ answerId, type });
      }
    } catch (error) {
      if (currentVote === type) {
        dispatch(addAnswerVoteOptimistic({ answerId, type }));
      } else if (currentVote && currentVote !== type) {
        dispatch(switchAnswerVoteOptimistic({ answerId, fromType: type, toType: currentVote }));
      } else {
        dispatch(removeAnswerVoteOptimistic({ answerId, type }));
      }

      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpvote = () => handleVote('UPVOTE');
  const handleDownvote = () => handleVote('DOWNVOTE');

  return {
    currentVote,
    isLoading,
    handleUpvote,
    handleDownvote,
    isUpvoted: currentVote === 'UPVOTE',
    isDownvoted: currentVote === 'DOWNVOTE',
  };
};

export const useQuestionLiveMode = (questionId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const [isSettingLiveMode, setIsSettingLiveMode] = useState(false);
  const forumQuestions = useSelector(selectForumQuestions);

  const question = forumQuestions.find((q) => q.id === questionId);
  const isLive = question?.isLive || false;

  const toggleLiveMode = async (setToLive: boolean) => {
    if (isSettingLiveMode) return;

    setIsSettingLiveMode(true);
    try {
      const result = await dispatch(
        updateQuestionLiveStatus({
          questionId,
          isLive: setToLive,
        }),
      ).unwrap();

      showToast({
        type: 'success',
        message: setToLive ? 'Live mode activated' : 'Live mode deactivated',
        description: setToLive
          ? 'Your question is now in live mode'
          : 'Your question is no longer in live mode',
      });

      return result;
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to update live status',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
      });
      return null;
    } finally {
      setIsSettingLiveMode(false);
    }
  };
  return {
    isLive,
    isSettingLiveMode,
    toggleLiveMode,
  };
};
