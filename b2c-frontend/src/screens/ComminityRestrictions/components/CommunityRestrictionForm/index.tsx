import React from 'react';
import { View, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import { setCommunityAccess, setIsRestricted } from '@/src/redux/slices/community/communitySlice';
import { RootState } from '@/src/redux/store';

const CommunityRestrictionForm = () => {
  const dispatch = useDispatch();
  const { access, isRestricted } = useSelector((state: RootState) => state.community);

  const handleAccessChange = (newAccess: 'PUBLIC' | 'PRIVATE') => {
    dispatch(setCommunityAccess(newAccess));
  };

  const handleRestrictionChange = () => {
    dispatch(setIsRestricted(!isRestricted));
  };

  return (
    <View className="py-6 bg-white">
      <Text className="text-2xl font-semibold text-gray-900 mb-8">Community restrictions</Text>
      <View className="mb-6">
        <RadioButton
          selected={access === 'PUBLIC'}
          onPress={() => handleAccessChange('PUBLIC')}
          label="Public"
          description="Anyone can view and contribute"
        >
          {access === 'PUBLIC' && (
            <ToggleSwitch
              enabled={isRestricted}
              onToggle={handleRestrictionChange}
              label="Restrict contributors"
            />
          )}
        </RadioButton>
      </View>

      <RadioButton
        selected={access === 'PRIVATE'}
        onPress={() => handleAccessChange('PRIVATE')}
        label="Private"
        description="Only forum members can view and contribute"
      >
        {access === 'PRIVATE' && (
          <ToggleSwitch
            enabled={isRestricted}
            onToggle={handleRestrictionChange}
            label="Restrict contributors"
          />
        )}
      </RadioButton>
    </View>
  );
};

export default CommunityRestrictionForm;
