import LeaderBoard from '@/src/components/LeaderBoard'
import SafeArea from '@/src/components/SafeArea'
import { useNavigation } from '@react-navigation/native'
import { Text } from 'react-native'

const LeaderBoardScreen = () => {
    const navigation = useNavigation()
    const onBack = () => {
        navigation.goBack()
    }
    return (
        <SafeArea>
            <LeaderBoard data={[]} onBack={onBack} />
        </SafeArea>
    )
}

export default LeaderBoardScreen