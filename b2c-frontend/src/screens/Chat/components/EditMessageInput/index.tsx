import { forwardRef } from 'react';
import { View, TextInput, Pressable, Text } from 'react-native';
import type { TextInput as TextInputType } from 'react-native';
import { EditMessageInputProps } from './types';

const EditMessageInput = forwardRef<TextInputType, EditMessageInputProps>(
  ({ value, onChangeText, onSave, onCancel, renderMedia }, ref) => {
    return (
      <View className="bg-[#F8F5FF] rounded-lg p-3">
        {renderMedia && renderMedia()}
        <TextInput
          ref={ref}
          value={value}
          onChangeText={onChangeText}
          className="bg-white border border-[#D2C9FF] rounded-md p-2 text-sm text-gray-900 min-h-[60px]"
          multiline
          autoFocus
          placeholder="Edit your message..."
        />
        <View className="flex-row justify-end mt-2 gap-2">
          <Pressable onPress={onCancel} className="py-1.5 px-3 rounded-md bg-gray-100">
            <Text className="text-xs text-gray-600 font-medium">Cancel</Text>
          </Pressable>
          <Pressable
            onPress={onSave}
            className={`py-1.5 px-3 rounded-md ${!value.trim() ? 'bg-violet-300 opacity-70' : 'bg-violet-600'}`}
            disabled={!value.trim()}
          >
            <Text className="text-xs text-white font-medium">Save</Text>
          </Pressable>
        </View>
      </View>
    );
  },
);

export default EditMessageInput;
