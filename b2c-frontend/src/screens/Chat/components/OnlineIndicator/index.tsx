import type React from 'react';
import { View } from 'react-native';
import { OnlineIndicatorProps } from './types';

const OnlineIndicator: React.FC<OnlineIndicatorProps> = ({ isOnline, size = 12 }) => {
  if (!isOnline) return null;

  return (
    <View
      className="absolute bottom-0 right-0 bg-green-500 rounded-full border-2 border-white"
      style={{
        width: size,
        height: size,
        transform: [{ translateX: 2 }, { translateY: 2 }],
      }}
    />
  );
};

export default OnlineIndicator;
