import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ForumQuestionsFiltersI } from '@/src/redux/slices/question/types';

export type UseForumFilterResultI = {
  filters: ForumQuestionsFiltersI;
  departmentSelection: SearchResultI | null;
  qnaTopicsSelection: SearchResultI[] | null;
  equipmentSelection: SearchResultI | null;
  makeSelection: SearchResultI | null;
  modelSelection: SearchResultI | null;
  updateFilter: (key: keyof ForumQuestionsFiltersI, value: any) => void;
  addTopic: (topic: SearchResultI) => void;
  removeTopic: (topicId: string) => void;
  setEquipmentCategory: (equipment: SearchResultI | null) => void;
  setEquipmentManufacturer: (manufacturer: SearchResultI | null) => void;
  setEquipmentModel: (model: SearchResultI | null) => void;
  applyFilters: (pendingFilters: ForumQuestionsFiltersI) => Promise<void>;
  clearAllFilters: () => void;
};
