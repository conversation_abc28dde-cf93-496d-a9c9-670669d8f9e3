/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { View, Text, Pressable } from 'react-native';
import { useDispatch } from 'react-redux';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import EntitySearch from '@/src/components/EntitySearch';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import { setMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ForumQuestionsFiltersI } from '@/src/redux/slices/question/types';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import useForumFilter from './useHook';

type FilterProps = {
  pendingFilters: ForumQuestionsFiltersI;
  setPendingFilters: React.Dispatch<React.SetStateAction<ForumQuestionsFiltersI>>;
};

const Filter = ({ pendingFilters, setPendingFilters }: FilterProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    filters,
    departmentSelection,
    qnaTopicsSelection,
    equipmentSelection,
    makeSelection,
    modelSelection,
    updateFilter,
    removeTopic,
  } = useForumFilter();

  const [qnaEnabled, setQnaEnabled] = useState(pendingFilters.type === 'NORMAL');
  const [troubleshootingEnabled, setTroubleshootingEnabled] = useState(
    pendingFilters.type === 'TROUBLESHOOT',
  );
  const [localTopics, setLocalTopics] = useState<SearchResultI[]>([]);

  const MAX_TOPICS = 3;

  useEffect(() => {
    setPendingFilters(pendingFilters);
  }, [pendingFilters]);

  useEffect(() => {
    setQnaEnabled(pendingFilters.type === 'NORMAL');
    setTroubleshootingEnabled(pendingFilters.type === 'TROUBLESHOOT');
  }, [pendingFilters.type]);

  useEffect(() => {
    setLocalTopics(pendingFilters.topics || []);
  }, [pendingFilters.topics]);

  useEffect(() => {
    if (!qnaTopicsSelection) return;

    setLocalTopics((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const newTopics = qnaTopicsSelection.filter((t) => !existingIds.has(t.id));
      const merged = [...prev, ...newTopics];

      if (merged.length > MAX_TOPICS) {
        showToast({
          type: 'error',
          message: 'Topic Limit Reached',
          description: `You can only select up to ${MAX_TOPICS} topics`,
        });

        const limitedTopics = merged.slice(0, MAX_TOPICS);
        dispatch(setMultipleSelections({ key: 'filterTopic', value: limitedTopics }));
        return limitedTopics;
      }

      return merged;
    });
  }, [qnaTopicsSelection]);

  const handleTopicRemove = (id: string | number) => {
    const idString = id.toString();
    const updatedTopics = localTopics.filter((t) => t.id !== idString);
    setLocalTopics(updatedTopics);
    setPendingFilters((prev) => ({ ...prev, topics: updatedTopics }));
  };

  const handleQuestionTypeChange = (type: 'NORMAL' | 'TROUBLESHOOT') => {
    setPendingFilters((prev) => ({ ...prev, type }));
    if (type === 'NORMAL') {
      setQnaEnabled(true);
      setTroubleshootingEnabled(false);
    } else {
      setQnaEnabled(false);
      setTroubleshootingEnabled(true);
    }
  };

  const handleToggleChange = (key: keyof ForumQuestionsFiltersI, value: boolean) => {
    setPendingFilters((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <View className="flex-1 p-4">
      <View className="py-4">
        <ToggleSwitch
          enabled={pendingFilters.isLive}
          onToggle={() => handleToggleChange('isLive', !pendingFilters.isLive)}
          label="Live questions only"
        />
      </View>
      <View className="py-2">
        <ToggleSwitch
          enabled={pendingFilters.myRecommended}
          onToggle={() => handleToggleChange('myRecommended', !pendingFilters.myRecommended)}
          label="Recommended questions only"
        />
      </View>
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-4">
        <Checkbox
          size={20}
          label="Questions I asked"
          checked={pendingFilters.myQuestion}
          onValueChange={(value) => setPendingFilters((prev) => ({ ...prev, myQuestion: value }))}
        />
        <Checkbox
          size={20}
          label="Questions I answered"
          checked={pendingFilters.myAnswered}
          onValueChange={(value) => setPendingFilters((prev) => ({ ...prev, myAnswered: value }))}
        />
        <Checkbox
          size={20}
          label="Questions from my communities"
          checked={pendingFilters.myCommunity}
          onValueChange={(value) => setPendingFilters((prev) => ({ ...prev, myCommunity: value }))}
        />
        <Checkbox
          size={20}
          label="Questions from all communities"
          checked={!pendingFilters.myCommunity}
          onValueChange={() => setPendingFilters((prev) => ({ ...prev, myCommunity: false }))}
        />
      </View>
      {/* <Text className="text-base font-medium text-black py-4">Posted when</Text> */}
      {/* <View className="flex-row gap-4">
        <View className="flex-1">
          <DatePicker title="From" selectedDate="" onDateChange={() => {}} />
        </View>
        <View className="flex-1">
          <DatePicker title="To" selectedDate="" onDateChange={() => {}} />
        </View>
      </View> */}
      {/* <Text className="text-base font-medium text-black py-4">Connections</Text> */}
      {/* <View className="flex-row gap-8">
        <View>
          <Checkbox size={20} label="1st" />
        </View>
        <View>
          <Checkbox size={20} label="2nd" />
        </View>
        <View>
          <Checkbox size={20} label="3rd" />
        </View>
      </View> */}
      <EntitySearch
        titleClassName="text-base text-black py-2"
        title="Department"
        selectionKey="filterDepartment"
        placeholder="Select department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
      <Text className="text-base font-medium text-black py-4">Question type</Text>
      <View className="flex-col gap-2">
        <RadioButton
          selected={qnaEnabled}
          onPress={() => handleQuestionTypeChange('NORMAL')}
          label="QnA"
          inFilter={true}
        />
        {qnaEnabled && (
          <View>
            <ChipInput
              title="Filter topics"
              placeholder="Add a topic"
              chips={localTopics}
              onRemove={handleTopicRemove}
              titleClassName="text-base text-black py-2"
            />
          </View>
        )}
        <RadioButton
          selected={troubleshootingEnabled}
          onPress={() => handleQuestionTypeChange('TROUBLESHOOT')}
          label="Troubleshooting"
          inFilter={true}
        />
        {troubleshootingEnabled && (
          <View className="flex-col gap-2">
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Equipment"
              selectionKey="filterEquipmentCategory"
              placeholder="Select equipment"
              data={equipmentSelection ? equipmentSelection.name : ''}
            />
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Make"
              selectionKey="filterEquipmentManufacturer"
              placeholder="Select make"
              data={makeSelection ? makeSelection.name : ''}
            />
            <EntitySearch
              titleClassName="text-base text-black py-2"
              title="Model"
              selectionKey="filterEquipmentModel"
              placeholder="Select model"
              data={modelSelection ? modelSelection.name : ''}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default Filter;
