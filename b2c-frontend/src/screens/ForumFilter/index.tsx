/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { ForumQuestionsFiltersI } from '@/src/redux/slices/question/types';
import Filter from './Filter';
import useForumFilter from './Filter/useHook';

const ForumFilterScreen = () => {
  const navigation = useNavigation();
  const { applyFilters, clearAllFilters, filters } = useForumFilter();
  const [pendingFilters, setPendingFilters] = useState<ForumQuestionsFiltersI>(filters);

  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  const handleClearFilters = () => {
    clearAllFilters();
    setPendingFilters({
      type: 'ALL',
      isLive: false,
      myRecommended: false,
      myQuestion: false,
      myAnswered: false,
      myCommunity: false,
      department: null,
      topics: [],
      equipmentCategory: null,
      equipmentManufacturer: null,
      equipmentModel: null,
    });
  };

  const handleApplyFilters = () => {
    if (pendingFilters) {
      applyFilters(pendingFilters);
    }
  };

  const insets = useSafeAreaInsets();

  return (
    <SafeArea>
      <View className="flex-row items-center justify-between px-4">
        <View className="flex-row items-center">
          <BackButton onBack={() => navigation.goBack()} label="" />
          <Text className="text-xl font-medium text-black">Filters</Text>
        </View>
        <View className="flex-row items-center gap-2">
          <Pressable className="px-2 items-center" onPress={handleClearFilters}>
            <Text className="text-base font-medium text-gray-600">Clear</Text>
          </Pressable>
          <Pressable className="px-4 items-center" onPress={handleApplyFilters}>
            <Text className="text-xl font-medium text-[#448600]">Apply</Text>
          </Pressable>
        </View>
      </View>
      <ScrollView
        showsVerticalScrollIndicator={true}
        contentContainerStyle={{
          paddingBottom: 85 + insets.bottom,
        }}
      >
        <Filter pendingFilters={pendingFilters} setPendingFilters={setPendingFilters} />
      </ScrollView>
    </SafeArea>
  );
};

export default ForumFilterScreen;
