import { View } from 'react-native';
import ExploreHead from '../ExploreHead';
import ExploreQnA from '../ExploreQna';
import { QnATagI } from '../ExploreQna/types';
import ExploreTroubleShoot from '../ExploreTroubleShoot';
import RecommendedCommunities from '../RecommendedCommunities';

const sampleTags: QnATagI[] = [
  { id: '1', text: 'Lorem Ipsum' },
  { id: '2', text: 'Lorem' },
  { id: '3', text: 'Lorem Ipsum Lorem' },
  { id: '4', text: 'Lorem Ipsum Lorem' },
  { id: '5', text: 'Lorem Ipsum' },
  { id: '6', text: 'Lorem' },
  { id: '7', text: 'Lorem' },
  { id: '8', text: 'Lorem Ipsum' },
  { id: '9', text: 'Lorem Ipsum Lorem' },
];

const ExploreForum = () => {
  return (
    <View>
      <ExploreHead />
      <ExploreQnA title="Explore QnA" tags={sampleTags} showAllText="Show All" />
      <ExploreTroubleShoot />
      <RecommendedCommunities />
    </View>
  );
};

export default ExploreForum;
