import React from 'react';
import { Text, View, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ChevronRight from '@/src/assets/svgs/ChevronRight';

const ExploreTroubleShoot = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const handleExplore = () => {
    navigation.navigate('ExploreTroubleShoot');
  };
  return (
    <Pressable
      className="bg-white border border-gray-200 rounded-2xl p-4 mx-4 mb-4 shadow-sm"
      onPress={handleExplore}
    >
      <View className="flex-row justify-between items-center">
        <View className="flex-1 pr-4">
          <Text className="text-black text-lg font-semibold mb-2">Explore troubleshooting</Text>
          <Text className="text-gray-500 text-sm leading-5">
            Select equipment, make and model to explore troubleshooting posts
          </Text>
        </View>
        <View className="ml-2">
          <ChevronRight width={2.5} height={2.5} />
        </View>
      </View>
    </Pressable>
  );
};

export default ExploreTroubleShoot;
