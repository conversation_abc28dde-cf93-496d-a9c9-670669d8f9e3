import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ShipCreateEditPayloadI } from '../../EditShipItem/components/EditShipItem/types';
import { EquipmentDetailsFormDataI, EquipmentPayloadI } from './EditEquipmentItem/types';

export const generatePayloadEquipment = (
  data: EquipmentDetailsFormDataI,
  preFilledData: ShipCreateEditPayloadI[],
  localFuelTypes: SearchResultI[],
  initialFuelTypes: SearchResultI[],
  equipmentId?: string,
) => {
  const deletedFuelTypes = initialFuelTypes
    .filter((initial) => !localFuelTypes.some((local) => local.id === initial.id))
    .map(({ id, dataType }) => ({ id, dataType }));

  const addedFuelTypes = localFuelTypes
    .filter((local) => !initialFuelTypes.some((initial) => initial.id === local.id))
    .map(({ id, dataType }) => ({ id, dataType }));

  const category: EquipmentPayloadI = {
    opr: equipmentId ? 'UPDATE' : 'CREATE',
    equipmentCategory: {
      id: data.category.id,
      dataType: data.category.dataType,
    },
    manufacturerName: data.manufacturerName,
    model: data.model,
    powerCapacity: parseFloat(data.power),
  };

  if (data.additionalDetails !== null) {
    category.details = data.additionalDetails;
  }

  if (equipmentId) {
    category['id'] = equipmentId;
  }

  category['fuelTypes'] = equipmentId
    ? [
        ...addedFuelTypes.map((item) => {
          return {
            opr: 'CREATE',
            fuelType: {
              id: item.id,
              dataType: item.dataType,
            },
          };
        }),
        ...deletedFuelTypes.map((item) => {
          return {
            id: item.id,
            opr: 'DELETE',
          };
        }),
      ]
    : localFuelTypes.map((item) => {
        return {
          opr: 'CREATE',
          fuelType: {
            id: item.id,
            dataType: item.dataType,
          },
        };
      });

  let payload = JSON.parse(JSON.stringify(preFilledData));
  payload[0].designations[0].ships[0]['equipmentCategories'] = [category];

  return payload;
};
