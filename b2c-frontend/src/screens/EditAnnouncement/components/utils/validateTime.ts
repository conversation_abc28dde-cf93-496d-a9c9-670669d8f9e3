const validateTime = (startTime: string | Date, endTime: string | Date) => {
  const start = typeof startTime === 'string' ? new Date(startTime) : startTime;
  const end = typeof endTime === 'string' ? new Date(endTime) : endTime;

  if (isNaN(start.getTime())) return 'Invalid start time';
  if (isNaN(end.getTime())) return 'Invalid end time';

  // Compare times (ignoring dates)
  const startHours = start.getHours();
  const startMinutes = start.getMinutes();
  const endHours = end.getHours();
  const endMinutes = end.getMinutes();

  if (endHours < startHours || (endHours === startHours && endMinutes <= startMinutes)) {
    return 'End time must be after start time';
  }

  return true;
};
