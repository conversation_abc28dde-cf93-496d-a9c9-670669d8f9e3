import { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import EntitySearch from '@/src/components/EntitySearch';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearLocationData } from '@/src/redux/slices/announcement/announcementSlice';
import type { AppDispatch } from '@/src/redux/store';

const City = () => {
  const navigation = useNavigation();
  const citySelection = useSelector(selectSelectionByKey('city'));
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (citySelection) {
      dispatch(clearLocationData());
      navigation.goBack();
    }
  }, [citySelection]);

  return (
    <EntitySearch
      title=""
      placeholder="Enter City"
      selectionKey="city"
      data={citySelection?.name}
      titleClassName="hidden"
      className="px-3"
    />
  );
};

export default City;
