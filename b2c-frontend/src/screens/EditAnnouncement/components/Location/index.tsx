import { type ReactNode, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { setLocationData } from '@/src/redux/slices/announcement/announcementSlice';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { AppDispatch } from '@/src/redux/store';
import City from './components/city';
import Map from './components/map';

const LocationScreen = () => {
  const navigation = useNavigation();
  const [selectedLocation, setSelectedLocation] = useState(null);
  const dispatch = useDispatch<AppDispatch>();

  const handleDoneClick = () => {
    if (selectedLocation) {
      dispatch(setLocationData(selectedLocation));
      dispatch(clearSelection('city'));
    }
  };

  const tabs = [
    { id: 'map', label: 'Map' },
    { id: 'city', label: 'City' },
  ];
  const [activeTab, setActiveTab] = useState('map');

  const tabComponents: { [key: string]: ReactNode } = {
    city: <City />,
    map: <Map onLocationSelect={setSelectedLocation} />,
  };

  return (
    <SafeArea>
      <View className="flex flex-row justify-between items-center">
        <BackButton onBack={() => navigation.goBack()} />
        <Pressable
          className="pr-3"
          onPress={() => {
            handleDoneClick();
            navigation.goBack();
          }}
        >
          <Text className={`text-lg font-medium text-[#448600]`}>Done</Text>
        </Pressable>
      </View>
      <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
      <View className="flex-1">{tabComponents[activeTab]}</View>
    </SafeArea>
  );
};

export default LocationScreen;
