import { useEffect, useState } from 'react';
import { Pressable, SafeAreaView, Text, TextInputComponent, View } from 'react-native';
import Config from 'react-native-config';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import DatePicker from '@/src/components/DatePicker';
import TextInput from '@/src/components/TextInput';
import TimePicker from '@/src/components/TimePicker';
import { selectLocationData } from '@/src/redux/selectors/announcement';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '@/src/screens/EditEducationItem/components/utils';
import { EditAnnouncementPropsI } from './types';
import { useEditAnnouncement } from './useHook';

const EditAnnouncement = ({ onBack, profileId, announcementId }: EditAnnouncementPropsI) => {
  const mapboxToken = Config.MAPBOX_ACCESS_TOKEN;

  const { methods, isSubmitting, setIsSubmitted, hasChanges, onSubmit, navigation, clearFields } =
    useEditAnnouncement(announcementId);

  const handleLocationPress = () => {
    navigation.navigate('Location');
  };

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;

  const startDate = watch('startDate');
  const endDate = watch('endDate');
  const startTime = watch('startTime');
  const endTime = watch('endTime');

  const [locationName, setLocationName] = useState('');
  const locationData = useSelector(selectLocationData);
  const citySelection = useSelector(selectSelectionByKey('city'));

  const location = citySelection
    ? citySelection.name
    : locationData.id
      ? locationName
      : 'Enter Location';

  useEffect(() => {
    if (!locationData.id) return;

    const fetchPlaceName = async () => {
      try {
        const [longitude, latitude] = locationData.geometry.coordinates;
        const response = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxToken}&types=place,locality,address`,
        );
        const data = await response.json();

        if (data.features?.length) {
          const address = data.features.find((item: any) => item.id.startsWith('address'));
          setLocationName(address?.place_name || '');
        }
      } catch (error) {
        console.error('Mapbox API error:', error);
      }
    };

    fetchPlaceName();
  }, [locationData.geometry.coordinates]);

  return (
    <SafeAreaView className="px-4">
      <View className="flex-row items-center justify-between py-4">
        <BackButton
          onBack={onBack}
          label={announcementId ? 'Edit Announcement' : 'Create Announcement'}
        />
        <Pressable
          onPress={() => {
            setIsSubmitted(true);
            onSubmit(methods.getValues());
          }}
          disabled={isSubmitting || !hasChanges}
        >
          <Text
            className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>
      <Controller
        control={control}
        name="title"
        rules={{ required: 'Title is required' }}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <TextInput
            label="Title"
            value={value}
            onChangeText={onChange}
            placeholder="Enter title"
            error={error?.message}
            className="py-3"
          />
        )}
      />
      <Controller
        control={control}
        name="message"
        rules={{ required: 'Message is required' }}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <TextInput
            label="Message"
            value={value}
            onChangeText={onChange}
            placeholder="Enter message"
            error={error?.message}
            className="py-3"
          />
        )}
      />
      <View className="flex-row gap-2 py-1">
        <View className="flex-1">
          <Controller
            control={control}
            name="startDate"
            rules={{
              required: 'Start date is required',
              validate: (value) => validateDate(value, endDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="Start Date"
                  selectedDate={startDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1">
          <Controller
            control={control}
            name="startTime"
            rules={{
              required: 'Start Time is required',
              validate: (value) => validateTime(value, endTime),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <TimePicker
                  title="Start Time"
                  selectedTime={startTime}
                  onTimeChange={(time) => {
                    if (time instanceof Date) {
                      onChange(time.toISOString().split('T')[1]);
                    }
                  }}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
      </View>

      <View className="flex-row gap-2 py-1">
        <View className="flex-1">
          <Controller
            control={control}
            name="endDate"
            rules={{
              required: 'End Date is required',
              validate: (value) => validateDate(startDate, value),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="End Date"
                  selectedDate={endDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1">
          <Controller
            control={control}
            name="endTime"
            rules={{
              required: 'End Time is required',
              validate: (value) => validateTime(startTime, value),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <TimePicker
                  title="End Time"
                  selectedTime={endTime}
                  onTimeChange={(time) => {
                    if (time instanceof Date) {
                      onChange(time.toISOString().split('T')[1]);
                    }
                  }}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
      </View>
      <Pressable onPress={handleLocationPress}>
        <Text className="text-sm font-medium text-black mb-2 leading-4">Location</Text>
        <View className="border border-[#D4D4D4] w-full px-4 py-4 rounded-xl bg-white">
          <Text
            className={`
                    ${location === 'Enter Location' ? 'text-gray-400' : 'text-black'}
                    text-base leading-4
                `}
          >
            {location}
          </Text>
        </View>
      </Pressable>
    </SafeAreaView>
  );
};

export default EditAnnouncement;
