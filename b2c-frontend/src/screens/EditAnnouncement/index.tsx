import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { HomeStackParamListI, ProfileStackParamsListI } from '@/src/navigation/types';
import EditAnnouncement from './components/EditAnnouncement';

type RouteProps = RouteProp<HomeStackParamListI, 'EditAnnouncement'>;

const EditAnnouncementScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId } = useSelector(selectCurrentUser);
  const navigation = useNavigation();
  const { announcementId } = route.params || {};

  return (
    <SafeArea>
      <EditAnnouncement
        onBack={navigation.goBack}
        profileId={profileId!}
        announcementId={announcementId!}
      />
    </SafeArea>
  );
};

export default EditAnnouncementScreen;
