import { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import type { Image } from 'react-native-image-crop-picker';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useSelector, useDispatch } from 'react-redux';
import * as z from 'zod';
import { selectCommunity } from '@/src/redux/selectors/community';
import {
  setCommunityAvatar,
  setCommunityAvatarImage,
  setMembers,
  updateCommunity,
} from '@/src/redux/slices/community/communitySlice';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string(),
  moderators: z.array(z.any()).optional(),
});

type FormValues = z.infer<typeof schema>;

const useCreateCommunityForm = () => {
  const [avatarFile, setAvatarFile] = useState<Image | null>(null);
  const [isAvatarDeleted, setIsAvatarDeleted] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const communityState = useSelector(selectCommunity);

  const methods = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: communityState.name || '',
      description: communityState.description || '',
      moderators: communityState.moderators || [],
    },
  });

  useEffect(() => {
    const subscription = methods.watch((value) => {
      dispatch(
        updateCommunity({
          name: value.name || '',
          description: value.description || '',
          moderators: value.moderators || [],
        }),
      );
    });
    return () => subscription.unsubscribe();
  }, [methods.watch(['description', 'moderators', 'name'])]);

  const getAvatarValue = () => {
    if (isAvatarDeleted) return null;
    if (avatarFile?.path) return avatarFile.path;
    return null;
  };

  const handleAvatarChange = async (image: Image) => {
    try {
      setLoading(true);
      setAvatarFile(image);
      setIsAvatarDeleted(false);
      dispatch(setCommunityAvatarImage(image));
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error selecting avatar.',
        description: 'Please try again',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarDelete = async () => {
    try {
      setLoading(true);
      setAvatarFile(null);
      setIsAvatarDeleted(true);
      dispatch(setCommunityAvatar(null));
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error deleting avatar.',
        description: 'Please try again',
      });
    } finally {
      setLoading(false);
    }
  };

  const {
    formState: { errors },
    watch,
  } = methods;
  const fields = watch();

  const handleNext = async () => {
    const isFormValid = await methods.trigger();

    if (!isFormValid) {
      let errorMessage = 'Please fill in all required fields:';
      if (errors?.name) {
        errorMessage += '\n- Name is required';
      }
      showToast({
        type: 'error',
        message: errorMessage,
      });
      return;
    }
    dispatch(updateCommunity({ ...fields, avatar: communityState.avatar }));
    dispatch(setMembers([]));
    navigation.navigate('CommunityRestrictions');
  };

  return {
    methods,
    getAvatarValue,
    handleAvatarChange,
    handleAvatarDelete,
    loading,
    isAvatarDeleted,
    avatarFile,
    handleNext,
  };
};

export default useCreateCommunityForm;
