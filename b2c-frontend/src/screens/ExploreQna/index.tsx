import { ScrollView, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ExploreQnA from '../Explore/components/ExploreQna';
import { QnATagI } from '../Explore/components/ExploreQna/types';

const sampleTags: QnATagI[] = [
  { id: '1', text: 'Lorem Ipsum' },
  { id: '2', text: 'Lorem' },
  { id: '3', text: 'Lorem Ipsum Lorem' },
  { id: '4', text: 'Lorem Ipsum Lorem' },
  { id: '5', text: 'Lorem Ipsum' },
  { id: '6', text: 'Lorem' },
  { id: '7', text: 'Lorem' },
  { id: '8', text: 'Lorem Ipsum' },
  { id: '9', text: 'Lorem Ipsum Lorem' },
  { id: '10', text: 'Lorem Ipsum' },
  { id: '12', text: 'Lorem' },
  { id: '13', text: 'Lorem Ipsum Lorem' },
  { id: '14', text: 'Lorem Ipsum Lorem' },
  { id: '15', text: 'Lorem Ipsum' },
  { id: '16', text: 'Lorem' },
  { id: '17', text: 'Lorem' },
  { id: '18', text: 'Lorem Ipsum' },
  { id: '19', text: 'Lorem Ipsum Lorem' },
  { id: '20', text: 'Lorem Ipsum' },
  { id: '22', text: 'Lorem' },
  { id: '23', text: 'Lorem Ipsum Lorem' },
  { id: '24', text: 'Lorem Ipsum Lorem' },
  { id: '25', text: 'Lorem Ipsum' },
  { id: '26', text: 'Lorem' },
  { id: '27', text: 'Lorem' },
  { id: '28', text: 'Lorem Ipsum' },
  { id: '29', text: 'Lorem Ipsum Lorem' },
];

const ExploreQnaScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const onBack = () => {
    navigation.goBack();
  };
  return (
    <SafeArea>
      <ScrollView>
        <View>
          <ExploreQnA title="Explore QnA" tags={sampleTags} />
        </View>
      </ScrollView>
    </SafeArea>
  );
};

export default ExploreQnaScreen;
