import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Checkbox from '@/src/components/Checkbox';
import EntitySearch from '@/src/components/EntitySearch';
import { selectQuestionIsGeneralDepartment } from '@/src/redux/selectors/question';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { setIsGeneral } from '@/src/redux/slices/question/questionSlice';

const TroubleshootingFields = () => {
  const dispatch = useDispatch();
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const equipmentCategorySelection = useSelector(selectSelectionByKey('equipmentCategory'));
  const equipmentManufacturerSelection = useSelector(selectSelectionByKey('equipmentManufacturer'));
  const equipmentModelSelection = useSelector(selectSelectionByKey('equipmentModel'));
  const imo = useSelector(selectSelectionByKey('ship')) as unknown as {
    imo: string;
    dataType: string;
    name: string;
  };
  const isGeneral = useSelector(selectQuestionIsGeneralDepartment);
  const handleDeleteImo = () => {
    dispatch(clearSelection('ship'));
  };

  const handleGeneralToggle = (value: boolean) => {
    dispatch(setIsGeneral(value));
    if (value) {
      dispatch(clearSelection('department'));
    }
  };

  return (
    <>
      <EntitySearch
        title={'IMO number (optional)'}
        placeholder={`Enter IMO number`}
        selectionKey="ship"
        data={imo ? imo.imo : ''}
        deletable={true}
        onDelete={handleDeleteImo}
      />

      <EntitySearch
        title={'Equipment'}
        placeholder={`Enter equipment`}
        selectionKey="equipmentCategory"
        data={equipmentCategorySelection ? equipmentCategorySelection.name : ''}
      />

      <EntitySearch
        title={'Make'}
        placeholder={`Enter Make`}
        selectionKey="equipmentManufacturer"
        data={equipmentManufacturerSelection ? equipmentManufacturerSelection.name : ''}
      />

      <EntitySearch
        title={'Model'}
        placeholder={`Enter Model`}
        selectionKey="equipmentModel"
        data={equipmentModelSelection ? equipmentModelSelection.name : ''}
      />

      <EntitySearch
        title={'Department type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
        className="my-0"
        editable={!isGeneral}
      />
      <View className="flex-row justify-between items-center">
        <View></View>
        <View>
          <Checkbox
            label="General(All Departments)"
            labelClassName="text-sm"
            onValueChange={(value) => {
              handleGeneralToggle(value);
            }}
            checked={isGeneral}
          />
        </View>
      </View>
    </>
  );
};

export default TroubleshootingFields;
