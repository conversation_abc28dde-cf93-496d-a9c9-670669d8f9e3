import { useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import Plus from '@/src/assets/svgs/Plus';
import Tick from '@/src/assets/svgs/Tick';
import MemberListHeader from '../MemberListHeader';
import { MemberListNavigationI } from './types';
import { useMemberList } from './useHook';

const MembersList = () => {
  const [contributorStatus, setContributorStatus] = useState<Record<string, boolean>>({});
  const { data, error, hasMore, loadMore, loading, refresh, refreshing } = useMemberList({
    forumId: 'default-forum',
  });

  const handleUserPress = (user: ListItem) => {};

  const toggleContributor = (profileId: string) => {
    setContributorStatus((prev) => ({
      ...prev,
      [profileId]: !prev[profileId],
    }));
  };

  const navigation = useNavigation<MemberListNavigationI>();
  const handleBack = () => {
    navigation.goBack();
  };

  if (loading && !data.length) {
    return (
      <View className="flex-1 bg-white">
        <MemberListHeader handleBack={handleBack} />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" />
        </View>
      </View>
    );
  }

  if (!data.length) {
    return (
      <View className="flex-1 bg-white">
        <MemberListHeader handleBack={handleBack} />
        <NotFound
          title="No members found"
          subtitle="When there are forum members, they will appear here"
        />
      </View>
    );
  }

  const renderContributorButton = (item: ListItem) => {
    const profileId = item.Profile?.id;
    if (!profileId) return null;

    const isContributor =
      contributorStatus[profileId] || item.status === 'ADMIN' || item.status === 'MODERATOR';

    return (
      <Pressable
        onPress={(e) => {
          e.stopPropagation();
          toggleContributor(profileId);
        }}
        className={`flex-row items-center px-3 py-2 rounded-full border ${
          isContributor ? 'bg-primaryGreen border-primaryGreen' : 'bg-white border-gray-300'
        }`}
      >
        {isContributor ? (
          <>
            <Tick width={1.5} height={1.5} color="white" />
            <Text className="text-white font-medium ml-1">Contributor</Text>
          </>
        ) : (
          <>
            <Plus width={1.5} height={1.5} fill="#6B7280" />
            <Text className="text-gray-700 font-medium ml-1">Contributor</Text>
          </>
        )}
      </Pressable>
    );
  };
  return (
    <View className="flex-1 bg-white">
      <View className="flex-row px-4 py-3 border-b border-gray-100">
        <MemberListHeader handleBack={handleBack} />
      </View>
      <UsersList
        data={data}
        loading={loading}
        onLoadMore={loadMore}
        onPress={handleUserPress}
        onRefresh={refresh}
        refreshing={refreshing}
        renderCustom={renderContributorButton}
      />
    </View>
  );
};

export default MembersList;
