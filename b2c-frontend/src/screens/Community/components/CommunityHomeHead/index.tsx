import { useState } from 'react';
import { ActivityIndicator, Pressable, Text, View, Share } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionsMenu, OptionItem } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import AddConnection from '@/src/assets/svgs/AddConnection';
import AddItem from '@/src/assets/svgs/AddItem';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import Search from '@/src/assets/svgs/Search';
import { default as ShareIcon } from '@/src/assets/svgs/Share';
import Tick from '@/src/assets/svgs/Tick';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { CommunityHomeHeadPropsI } from './types';
import { formatMemberCount } from './utils';

const CommunityHomeHead = ({
  communityName = 'Community Name',
  memberCount = 0,
  avatarUri = 'https://randomuser.me/api/portraits/men/75.jpg',
  onConnect,
  onDisconnect,
  onRevokeRequest,
  initialConnectionState = 'disconnected',
  role,
}: CommunityHomeHeadPropsI) => {
  const [connecting, setConnecting] = useState(false);
  const [connectionState, setConnectionState] = useState(initialConnectionState);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalAction, setModalAction] = useState<
    'connect' | 'disconnect' | 'revoke' | 'delete' | 'report' | null
  >(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const navigation = useNavigation();

  const onBack = () => {
    navigation.goBack();
  };

  const handleConnectionAction = async (action: 'connect' | 'disconnect' | 'revoke') => {
    if (connecting) return;

    setConnecting(true);

    try {
      switch (action) {
        case 'connect':
          onConnect?.();
          setConnectionState('requested');
          break;
        case 'disconnect':
          onDisconnect?.();
          setConnectionState('disconnected');
          break;
        case 'revoke':
          onRevokeRequest?.();
          setConnectionState('disconnected');
          break;
      }
    } catch (error) {
      console.error('Connection action failed:', error);
    } finally {
      setConnecting(false);
    }
  };

  const handleShowConfirmation = (action: 'connect' | 'disconnect' | 'revoke') => {
    setModalAction(action);
    setIsModalVisible(true);
  };

  const handleConfirmAction = async () => {
    if (!modalAction) return;

    try {
      if (modalAction === 'delete') {
      } else if (modalAction === 'report') {
      } else {
        await handleConnectionAction(modalAction);
      }
    } catch (error) {
      console.error('Connection action failed:', error);
    } finally {
      setIsModalVisible(false);
      setModalAction(null);
    }
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setTimeout(() => {
      setModalAction(null);
    }, 300);
  };

  const ConnectionButton = () => {
    const buttonConfigs = {
      connected: {
        style: 'bg-white border border-gray-200',
        textStyle: 'text-[#448600]',
        iconColor: '#448600',
        text: 'Joined',
        onPress: () => handleShowConfirmation('disconnect'),
      },
      requested: {
        style: 'bg-[#448600] border border-[#448600]',
        textStyle: 'text-white',
        iconColor: 'white',
        text: 'Requested',
        onPress: () => handleShowConfirmation('revoke'),
      },
      disconnected: {
        style: 'bg-[#448600] border border-[#448600]',
        textStyle: 'text-white',
        iconColor: 'white',
        text: 'Join',
        onPress: () => handleConnectionAction('connect'),
      },
    };

    const config = buttonConfigs[connectionState];

    return (
      <Pressable
        onPress={config.onPress}
        disabled={connecting}
        className={`flex-row items-center justify-center rounded-lg px-3 py-2 min-w-[80px] ${config.style}`}
        accessibilityLabel={`${config.text} with community`}
        accessibilityRole="button"
      >
        <View className="mr-2">
          {connecting ? (
            <ActivityIndicator size="small" color={config.iconColor} />
          ) : connectionState === 'disconnected' ? (
            <AddConnection color={config.iconColor} width={1.5} height={1.5} />
          ) : (
            <Tick color={config.iconColor} width={1.5} height={1.5} />
          )}
        </View>
        <Text className={`font-medium ${config.textStyle}`}>{config.text}</Text>
      </Pressable>
    );
  };

  const getModalDescription = () => {
    switch (modalAction) {
      case 'disconnect':
        return 'Are you sure you want to leave this community?';
      case 'revoke':
        return 'Are you sure you want to revoke your request to join this community?';
      case 'delete':
        return 'Are you sure you want to delete this community? This action cannot be undone.';
      case 'report':
        return 'Are you sure you want to report this community?';
      default:
        return '';
    }
  };

  const getModalTitle = () => {
    switch (modalAction) {
      case 'delete':
        return 'Delete Community';
      case 'report':
        return 'Report Community';
      default:
        return 'Confirm Action';
    }
  };

  return (
    <>
      <View className="flex-row items-start justify-between px-4 py-3 bg-white border-b border-borderGrayExtraLight">
        <View className="flex-1 flex-row items-start">
          <BackButton label="" onBack={onBack} />

          <View className="flex-1 flex-row items-start ml-2">
            <UserAvatar avatarUri={avatarUri} name={communityName} width={48} height={48} />

            <View className="flex-1 ml-3">
              <Text
                className="text-base font-medium text-labelBlack leading-4"
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {communityName}
              </Text>

              <Text className="text-sm text-gray-600 mt-1">{formatMemberCount(memberCount)}</Text>

              <View className="flex-row items-center mt-2 gap-2">
                <ConnectionButton />

                <View className="flex-row gap-4">
                  <Search width={3} height={3} color="#374151" />
                  <AddItem width={3} height={3} />
                </View>
              </View>
            </View>
          </View>
        </View>

        <Pressable
          className="p-1"
          accessibilityLabel="More options"
          accessibilityRole="button"
          onPress={() => setIsBottomSheetVisible(true)}
        >
          <HorizontalEllipsis width={2} height={2} color="black" />
        </Pressable>
      </View>

      <View>
        <CustomModal
          isVisible={isModalVisible}
          title={getModalTitle()}
          description={getModalDescription()}
          confirmText={capitalizeFirstLetter(modalAction ?? '')}
          confirmButtonVariant="danger"
          cancelText="Cancel"
          onConfirm={handleConfirmAction}
          onCancel={handleModalClose}
          isConfirming={connecting}
        />
      </View>
      <BottomSheet
        visible={isBottomSheetVisible}
        onClose={() => setIsBottomSheetVisible(false)}
        onModalHide={() => {
          if (modalAction) {
            setIsModalVisible(true);
          }
        }}
      >
        <OptionsMenu>
          {role === 'admin' && (
            <>
              <OptionItem
                icon={<AddItem color="black" />}
                label="Add Members"
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  (navigation as any).navigate('LearnCollabStack', {
                    screen: 'CommunityMembers',
                    params: { forumId: '123' },
                  });
                }}
              />
              <OptionItem
                icon={<TrashBin color="red" />}
                label="Delete Community"
                textClassName="text-red-500"
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  setModalAction('delete');
                }}
              />
            </>
          )}
          {role === 'member' && (
            <OptionItem
              icon={<ReportFlag color="red" />}
              label="Report"
              textClassName="text-red-500"
              onPress={() => {
                setIsBottomSheetVisible(false);
                setModalAction('report');
              }}
            />
          )}
          <OptionItem
            icon={<ShareIcon color="black" />}
            label="Share"
            onPress={async () => {
              setIsBottomSheetVisible(false);
              try {
                await Share.share({
                  message: `Check out this community: ${communityName}`,
                });
              } catch (error) {
                console.error('Error sharing:', error);
              }
            }}
          />
        </OptionsMenu>
      </BottomSheet>
    </>
  );
};

export default CommunityHomeHead;
