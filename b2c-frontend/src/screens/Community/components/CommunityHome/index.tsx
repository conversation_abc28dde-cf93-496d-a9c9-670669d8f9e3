import React from 'react';
import { View, FlatList, Pressable } from 'react-native';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';
import useForumPostList from '@/src/screens/Forum/components/ForumPostList/useHook';
import CommunityHomeHead from '../CommunityHomeHead';
import { CommunityHomePropsI } from './types';

const CommunityHome: React.FC<CommunityHomePropsI> = ({ posts }) => {
  const { handleSelectPost } = useForumPostList();

  const renderForumPost = ({ item }: { item: ForumPostProps }) => (
    <Pressable onPress={() => handleSelectPost(item)}>
      <ForumPost post={item} />
    </Pressable>
  );

  return (
    <View className="flex-1 bg-gray-50">
      <CommunityHomeHead communityName="Community Name" memberCount={12} role="admin" />
      <FlatList
        data={posts}
        renderItem={renderForumPost}
        keyExtractor={(item) => item.postId}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 20,
        }}
      />
    </View>
  );
};

export default CommunityHome;
