import { useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectForumAnswers,
  selectForumAnswersTotal,
  selectForumAnswersNextCursorId,
  selectForumAnswerLoading,
  selectForumAnswerError,
  selectForumAnswerDetail,
  selectAnswerVotes,
  selectAnswerVotesTotal,
  selectAnswerVotesNextCursorId,
  selectAnswerVoteLoading,
  selectAnswerVoteError,
} from '@/src/redux/selectors/forum';
import {
  fetchAnswersForQuestion,
  fetchAnswerDetail,
  createAnswer,
  deleteAnswer,
  removeAnswerOptimistic,
  fetchAnswerVotes,
  createAnswerVote,
} from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import type {
  ForumAnswerCreateOnePayloadI,
  ForumAnswerDeleteOnePayloadI,
} from '@/src/networks/answer/types';
import type {
  ForumAnswerVoteFetchManyParamsI,
  ForumAnswerVoteCreateOnePayloadI,
} from '@/src/networks/answerVote/types';

export const useAnswers = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = useState(false);
  const [expandedTexts, setExpandedTexts] = useState<Record<string, string>>({});
  const [loadingTexts, setLoadingTexts] = useState<Record<string, boolean>>({});

  const answers = useSelector(selectForumAnswers);
  const total = useSelector(selectForumAnswersTotal);
  const nextCursorId = useSelector(selectForumAnswersNextCursorId);
  const loading = useSelector(selectForumAnswerLoading);
  const error = useSelector(selectForumAnswerError);
  const answerDetail = useSelector(selectForumAnswerDetail);

  const getAnswersForQuestion = useCallback(
    async (questionId: string, cursorId?: number | null, pageSize: number = 10) => {
      try {
        await dispatch(fetchAnswersForQuestion({ questionId, cursorId, pageSize })).unwrap();
      } catch (error) {
        const errorMessage = `Failed to fetch answers: ${
          error instanceof APIResError ? error.message : 'Unknown error'
        }`;
        showToast({ message: errorMessage, type: 'error' });
      }
    },
    [dispatch],
  );

  const handleRefresh = useCallback(
    async (questionId: string) => {
      if (!questionId) return;
      setRefreshing(true);
      try {
        await dispatch(
          fetchAnswersForQuestion({ questionId, cursorId: null, pageSize: 10 }),
        ).unwrap();
      } catch (error) {
        showToast({
          message: 'Failed to refresh answers',
          type: 'error',
          description: error instanceof APIResError ? error.message : 'Unknown error',
        });
      } finally {
        setRefreshing(false);
      }
    },
    [dispatch],
  );

  const handleLoadMore = useCallback(
    async (questionId: string) => {
      if (!questionId || loading || !nextCursorId || answers.length === total) return;

      try {
        await dispatch(
          fetchAnswersForQuestion({ questionId, cursorId: nextCursorId, pageSize: 10 }),
        ).unwrap();
      } catch (error) {
        showToast({
          message: 'Failed to load more answers',
          type: 'error',
        });
      }
    },
    [dispatch, loading, nextCursorId],
  );

  useEffect(() => {
    if (answerDetail && answerDetail.id) {
      setExpandedTexts((prev) => ({
        ...prev,
        [answerDetail.id]: answerDetail.text,
      }));
      setLoadingTexts((prev) => ({
        ...prev,
        [answerDetail.id]: false,
      }));
    }
  }, [answerDetail]);

  const getMoreText = useCallback(
    async (answerId: string) => {
      if (loadingTexts[answerId]) return null;

      setLoadingTexts((prev) => ({
        ...prev,
        [answerId]: true,
      }));

      try {
        const result = await dispatch(fetchAnswerDetail(answerId)).unwrap();

        setExpandedTexts((prev) => ({
          ...prev,
          [answerId]: result.text,
        }));

        setLoadingTexts((prev) => ({
          ...prev,
          [answerId]: false,
        }));

        return result.text;
      } catch (error) {
        setLoadingTexts((prev) => ({
          ...prev,
          [answerId]: false,
        }));
        showToast({
          message: 'Failed to fetch answer details',
          type: 'error',
        });
        return null;
      }
    },
    [dispatch, loadingTexts],
  );

  const isTextExpanded = useCallback(
    (answerId: string) => !!expandedTexts[answerId],
    [expandedTexts],
  );

  const isTextLoading = useCallback((answerId: string) => !!loadingTexts[answerId], [loadingTexts]);

  const getFullText = useCallback(
    (answerId: string, originalText: string) => {
      return expandedTexts[answerId] || originalText;
    },
    [expandedTexts],
  );

  const createForumAnswer = useCallback(
    async (payload: ForumAnswerCreateOnePayloadI) => {
      const tempId = `temp_${Date.now()}`;
      try {
        await dispatch(createAnswer({ ...payload, tempId })).unwrap();
      } catch (error) {
        showToast({
          message: 'Failed to create answer',
          type: 'error',
          description: error instanceof APIResError ? error.message : 'Unknown error',
        });
      }
    },
    [dispatch],
  );

  const deleteForumAnswer = useCallback(
    async (answerId: string) => {
      try {
        dispatch(removeAnswerOptimistic({ answerId }));
        const payload: ForumAnswerDeleteOnePayloadI = { answerId: answerId };
        await dispatch(deleteAnswer(payload)).unwrap();
        showToast({
          message: 'Answer deleted',
          type: 'success',
        });
      } catch (error) {
        showToast({
          message: 'Failed to delete answer',
          type: 'error',
          description: error instanceof APIResError ? error.message : 'Unknown error',
        });
      }
    },
    [dispatch, getAnswersForQuestion],
  );

  return {
    answers,
    total,
    nextCursorId,
    loading,
    error,
    answerDetail,
    refreshing,
    getAnswersForQuestion,
    handleLoadMore,
    handleRefresh,
    createForumAnswer,
    deleteForumAnswer,
    getMoreText,
    isTextExpanded,
    isTextLoading,
    getFullText,
  };
};
