import { ForumAnswerWithProfileI } from '@/src/networks/answer/types';
import { ForumAnswerProps } from './Answers/types';

export const mapForumAnswers = (
  answers: ForumAnswerWithProfileI[],
  questionId: string,
): ForumAnswerProps[] => {
  return answers.map((answer) => ({
    answerId: answer.id,
    postId: questionId,
    canModify: answer.canModify,
    profile: answer.profile,
    content: answer.text,
    answerVerified: (answer as any).status === 'VERIFIED_SOLUTION',
    status: (answer as any).status,
    upVotes: answer.upvoteCount,
    downVotes: answer.downvoteCount,
    comments: answer.commentCount,
    commentView: true,
    userVote: answer.userVote,
    canUpdateStatus: answer.canUpdateStatus,
    attachments: answer.media || [],
  }));
};
