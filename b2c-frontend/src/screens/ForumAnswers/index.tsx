/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ActivityIndicator,
  Text,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import NotFound from '@/src/components/NotFound';
import SafeArea from '@/src/components/SafeArea';
import {
  selectForumQuestion,
  selectForumLoading,
  selectForumError,
} from '@/src/redux/selectors/forum';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import type { ForumAnswerWithProfileForQuestionI } from '@/src/networks/question/types';
import AnswerInput from './AnswerInput';
import { AttachmentI } from './AnswerInput/types';
import AnswerListSkeleton from './AnswerListSkeleton';
import AnswersList from './AnswersList';
import { useAnswers } from './AnswersList/useHook';
import { mapForumAnswers } from './utils';

const ForumAnswersScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const question = useSelector(selectForumQuestion);
  const loading = useSelector(selectForumLoading);
  const error = useSelector(selectForumError);

  const { createForumAnswer } = useAnswers();
  const {
    answers,
    loading: answersLoading,
    error: answersError,
    getAnswersForQuestion,
  } = useAnswers();

  const [isFetchingAnswers, setIsFetchingAnswers] = useState(false);

  useEffect(() => {
    if (question?.id) {
      setIsFetchingAnswers(true);
      getAnswersForQuestion(question.id, null, 10).finally(() => {
        setIsFetchingAnswers(false);
      });
    }
  }, [question?.id, getAnswersForQuestion]);

  const handleSubmit = (text: string, files: AttachmentI[]) => {
    if (!question?.id) return;
    createForumAnswer({ text, questionId: question.id, files: files });
  };

  const mappedAnswers = mapForumAnswers(answers || [], question?.id ?? '');
  const isInitialLoading = (loading || answersLoading) && (!answers || answers.length === 0);

  if (isInitialLoading || isFetchingAnswers) {
    return (
      <SafeArea>
        <AnswerListSkeleton />
      </SafeArea>
    );
  }

  if (!question) {
    return (
      <SafeArea>
        <NotFound title="No Question Found" />
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
          </View>

          <AnswersList answers={mappedAnswers} question={question} />
          <View className="mb-4">
            <AnswerInput onSubmit={handleSubmit} />
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumAnswersScreen;
