/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState, useEffect } from 'react';
import { View, Text, Pressable, Image, GestureResponderEvent, Share } from 'react-native';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Clipboard from '@react-native-clipboard/clipboard';
import { useDispatch, useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { updateAnswerStatus } from '@/src/redux/slices/forum/forumSlice';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import FileViewer from '@/src/screens/Forum/components/FileViewer';
import { useAnswerVoting } from '@/src/screens/Forum/components/ForumPost/useHook';
import { getPreviewIconsFromAttachments } from '@/src/screens/Forum/components/ForumPost/utils';
import { previewIconMap } from '@/src/screens/Forum/components/ForumPost/utils';
import AiBot from '@/src/assets/images/others/aibot.png';
import Comment from '@/src/assets/svgs/Comment';
import Copy from '@/src/assets/svgs/Copy';
import DownVote from '@/src/assets/svgs/DownVote';
import EditPencil from '@/src/assets/svgs/EditPencil';
// Testing with a bot avatar
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import ShareIcon from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import Tick from '@/src/assets/svgs/Tick';
import TrashBin from '@/src/assets/svgs/TrashBin';
import UpVote from '@/src/assets/svgs/UpVote';
import { AnswerStatusE } from '@/src/networks/answer/types';
import { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import { useAnswers } from '../AnswersList/useHook';
import { ForumAnswerProps } from './types';

type AnswersProps = {
  answer: ForumAnswerProps;
  question: ForumQuestionDetailWithAnswersI;
  // onVoteUpdate?: (answerId: string, newVote: 'UPVOTE' | 'DOWNVOTE' | null, upVoteDelta: number, downVoteDelta: number) => void;
};

const Answers: React.FC<AnswersProps> = ({ answer, question }) => {
  const {
    answerId,
    canModify,
    profile,
    content,
    upVotes,
    downVotes,
    comments,
    answerVerified,
    userVote = null,
    canUpdateStatus,
    status,
    attachments,
  } = answer;
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();

  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  const { deleteForumAnswer, getMoreText, isTextExpanded, isTextLoading, getFullText } =
    useAnswers();
  const MAX_TEXT_LENGTH = 150;

  const {
    handleUpvote,
    handleDownvote,
    isUpvoted,
    isDownvoted,
    isLoading: voteLoading,
  } = useAnswerVoting(answerId);

  const [optionsVisible, setOptionsVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDeleteAction, setPendingDeleteAction] = useState(false);
  const [fileViewerVisible, setFileViewerVisible] = useState(false);

  const isExpanded = isTextExpanded(answerId);
  const isLoading = isTextLoading(answerId);
  const shouldShowReadMore = content.length >= MAX_TEXT_LENGTH && !isExpanded;

  const handleOptions = () => setOptionsVisible(true);

  const handleCloseOptions = () => setOptionsVisible(false);

  const handleBottomSheetHide = () => {
    if (pendingDeleteAction) {
      setDeleteModalVisible(true);
      setPendingDeleteAction(false);
    }
  };

  const showDeleteConfirmation = () => {
    setPendingDeleteAction(true);
    setOptionsVisible(false);
  };

  const showVerifyConfirmation = () => {
    setVerifyModalVisible(true);
  };

  const confirmVerify = async () => {
    setIsVerifying(true);
    try {
      await dispatch(
        updateAnswerStatus({ id: answerId, status: AnswerStatusE.VERIFIED_SOLUTION }) as any,
      ).unwrap();
      setVerifyModalVisible(false);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to verify',
        description: 'There was an error verifying this answer',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const cancelVerify = () => setVerifyModalVisible(false);

  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteForumAnswer(answerId);
      setDeleteModalVisible(false);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to delete',
        description: 'There was an error deleting your answer',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDelete = () => setDeleteModalVisible(false);

  const handleEdit = () => {
    handleCloseOptions();
  };

  const handleReport = () => {
    showToast({
      type: 'success',
      message: 'Reported',
      description: 'Post reported',
    });
    handleCloseOptions();
  };

  const handleCopyLink = () => {
    const postUrl = `https://network.navicater.com/forum/${question.id}`;
    Clipboard.setString(postUrl);
    showToast({
      type: 'success',
      message: 'Link Copied',
      description: 'Post link copied to clipboard',
    });
    handleCloseOptions();
  };

  const handleShare = async () => {
    try {
      const shareUrl = `https://network.navicater.com/forum/${question.id}`;
      const shareMessage = `🚀 Hey there!. just shared an absolutely amazing answer on Navicater! 🌟 Don't miss out on this insightful content—check it out now and be inspired! 💡✨ \n\n${shareUrl}\n\n#Navicater #GreatContent`;

      const result = await Share.share({
        message: shareMessage,
      });

      if (result.action === Share.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your answer has been shared 🎉',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Sharing Failed',
        description: 'Oops! Something went wrong. Try again.',
      });
    }
  };

  const handleReadMore = (e: GestureResponderEvent) => {
    e.stopPropagation();
    getMoreText(answerId);
  };

  const displayText = getFullText(answerId, content);
  const formattedText = shouldShowReadMore
    ? `${displayText.substring(0, MAX_TEXT_LENGTH)}...`
    : displayText;

  return (
    <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
      <View className="px-4 flex-row items-center justify-between">
        <View className="flex-row gap-2 items-center">
          <Pressable>
            <UserAvatar avatarUri={profile?.avatar} name={profile.name} width={28} height={28} />
          </Pressable>
          <Text className="text-base font-normal text-[#262626]">
            {profile?.name || 'Anonymous'}
          </Text>
        </View>
        <View className="flex-row gap-2 items-center">
          {status === AnswerStatusE.VERIFIED_SOLUTION ? (
            <View className="rounded-2xl px-2 py-1 flex-row items-center gap-2">
              <SolvedIcon width={2} height={2} />
            </View>
          ) : (
            canUpdateStatus && (
              <Pressable onPress={showVerifyConfirmation}>
                <Text className="text-base items-center font-medium text-[#448600]">Verify</Text>
              </Pressable>
            )
          )}
          <Pressable onPress={handleOptions}>
            <HorizontalEllipsis width={2.5} height={2.5} />
          </Pressable>
        </View>
      </View>
      <View className="px-4 py-2">
        <Text className="pl-10 text-base font-normal text-[#262626]">{formattedText}</Text>
        {shouldShowReadMore && (
          <Pressable
            onPress={handleReadMore}
            className="mt-2 pl-10"
            hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
            disabled={isLoading}
          >
            <Text className="text-green-800 font-medium">
              {isLoading ? 'Loading...' : 'See More'}
            </Text>
          </Pressable>
        )}
        {attachments && attachments.length > 0 && (
          <View className="px-2 pl-10">
            <FlatList
              horizontal
              data={getPreviewIconsFromAttachments(attachments)}
              contentContainerStyle={{
                flexGrow: 1,
              }}
              keyExtractor={(item, idx) => item + idx}
              renderItem={({ item }) => {
                const IconComponent = previewIconMap[item];
                return (
                  <Pressable className="p-2" onPress={() => setFileViewerVisible(true)}>
                    <IconComponent />
                  </Pressable>
                );
              }}
            />
          </View>
        )}
      </View>

      <View className="flex-row justify-between items-center px-4 py-2">
        <View className="flex-row items-center gap-5 pl-10">
          <View className="flex-row items-center gap-2">
            <Pressable onPress={handleUpvote} disabled={voteLoading}>
              <UpVote isLiked={isUpvoted} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
          </View>
          <View className="flex-row items-center gap-2">
            <Pressable onPress={handleDownvote} disabled={voteLoading}>
              <DownVote isLiked={isDownvoted} />
            </Pressable>
            <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
          </View>
          <View>
            <Pressable
              onPress={() =>
                navigation.navigate('ForumComments', {
                  postId: answer.answerId,
                  type: 'FORUM_ANSWER',
                })
              }
              className="flex-row items-center gap-2"
            >
              <Comment color="#525252" />
              <Text className="text-[#262626] text-sm font-medium">{comments}</Text>
            </Pressable>
          </View>
        </View>
        <Pressable onPress={handleShare}>
          <ShareIcon />
        </Pressable>
      </View>
      <BottomSheet
        height={canModify ? 230 : 200}
        visible={optionsVisible}
        onClose={handleCloseOptions}
        onModalHide={handleBottomSheetHide}
      >
        <OptionsMenu>
          {canModify ? (
            <>
              <OptionItem
                icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
                label="Delete answer"
                textClassName="text-red-500"
                onPress={showDeleteConfirmation}
              />
            </>
          ) : (
            <OptionItem
              icon={<ReportFlag stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Report answer"
              onPress={handleReport}
            />
          )}
          <View className="h-[1px] bg-gray-200 my-2" />
          <OptionItem
            icon={<Copy stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
            label="Copy Link"
            onPress={handleCopyLink}
          />
          <View className="h-[1px] bg-gray-200 my-2" />
          {/* {isOwnPost && (
            <OptionItem
              icon={<EditPencil stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Edit answer"
              onPress={handleEdit}
            />
          )} */}
        </OptionsMenu>
      </BottomSheet>
      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Answer"
        description="Are you sure you want to delete this answer? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isConfirming={isDeleting}
      />
      <CustomModal
        isVisible={verifyModalVisible}
        title="Verify Answer"
        description="Are you sure you want to mark this answer as verified? This will indicate that it is the correct solution."
        confirmText="Confirm"
        confirmButtonVariant="default"
        cancelText="Cancel"
        onConfirm={confirmVerify}
        onCancel={cancelVerify}
        isConfirming={isVerifying}
      />
      {fileViewerVisible && attachments && attachments.length > 0 && (
        <FileViewer
          isVisible={fileViewerVisible}
          onClose={() => setFileViewerVisible(false)}
          attachments={attachments}
        />
      )}
    </View>
  );
};

export default Answers;
