import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';

const ExploreTroubleShootForm = () => {
  const navigation = useNavigation();
  const onBack = () => {
    navigation.goBack();
  };
  return (
    <View className="flex-1 bg-white p-3">
      <View className="flex-row justify-between items-center">
        <BackButton
          label="Explore troubleshooting"
          labelClassname="font-medium text-lg leading-6"
          onBack={onBack}
        />
        <View className="flex-row items-center gap-2">
          <Pressable>
            <Text className="text-primaryGreen text-sm font-medium">Skip</Text>
          </Pressable>
          <Button
            label="Next"
            onPress={() => {}}
            variant={'primary'}
            className="rounded-full bg-primaryGreen w-auto"
          ></Button>
        </View>
      </View>
      <EntitySearch
        title={'Equipment'}
        placeholder={`Enter equipment`}
        selectionKey="ship"
        searchWithoutInput
      />
      <EntitySearch title={'Make'} placeholder={`Enter make`} selectionKey="make" />
      <EntitySearch title={'Model'} placeholder={`Enter model`} selectionKey="model" />
    </View>
  );
};

export default ExploreTroubleShootForm;
