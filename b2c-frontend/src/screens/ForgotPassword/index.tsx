/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { ForgotPasswordForm } from './components/ForgotPasswordForm';

export function ForgotPasswordScreen() {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <ForgotPasswordForm onBack={handleBack} />
    </SafeArea>
  );
}

export default ForgotPasswordScreen;
