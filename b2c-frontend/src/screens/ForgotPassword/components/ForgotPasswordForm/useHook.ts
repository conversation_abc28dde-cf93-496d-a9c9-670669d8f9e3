import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import {
  sendOTPForPasswordResetAsync,
  setForgotPasswordEmail,
} from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { ForgotPasswordFormData } from './types';

export function useForgotPassword(onSuccess?: (email: string) => void) {
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const methods = useForm<ForgotPasswordFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
    },
  });

  const handleEmailSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsSubmitting(true);
      const resultAction = await dispatch(sendOTPForPasswordResetAsync({ email: data.email }));

      if (sendOTPForPasswordResetAsync.fulfilled.match(resultAction)) {
        dispatch(setForgotPasswordEmail(data.email));
        showToast({
          type: 'success',
          message: 'Code Sent',
          description: 'Please check your email for the verification code',
        });
        onSuccess?.(data.email);
      } else {
        throw new Error('Failed to send verification code');
      }
    } catch (error: unknown) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            type: 'error',
            message: 'Error occured',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Error occured',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    handleEmailSubmit,
  };
}
