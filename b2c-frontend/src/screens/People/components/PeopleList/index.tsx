import { useState } from 'react';
import { ActivityIndicator, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { FlatList } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import TextInput from '@/src/components/TextInput';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCommunity } from '@/src/redux/selectors/community';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateCommunityHeader from '@/src/screens/CreateCommunity/components/CreateCommunityHeader';
import Tick from '@/src/assets/svgs/Tick';
import { FetchFollowersDataI } from '@/src/networks/connect/types';
import { AuthorProfileI } from '@/src/networks/content/types';
import usePeopleList from './useHook';

const PeopleList = () => {
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'People'>>();
  const { type, title, btnText } = route.params;
  const communityState = useSelector(selectCommunity);

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<AuthorProfileI[]>(() => {
    if (title === 'Add moderator') {
      return communityState.moderators || [];
    } else if (title === 'Add members' || title === 'Add people') {
      return communityState.members || [];
    } else {
      return communityState.members || [];
    }
  });

  const { handleSubmit, people, loading, isLoadingMore, loadMore, updateCommunityState } =
    usePeopleList(type, searchQuery);

  const toggleUserSelection = (user: AuthorProfileI): void => {
    const isModeratorTitle = title === 'Add moderator';
    const isMemberTitle =
      title === 'Add members' || title === 'Add people' || title !== 'Add moderator';

    const isAlreadyModerator = communityState.moderators?.some((u) => u.id === user.id) || false;
    const isAlreadyMember = communityState.members?.some((u) => u.id === user.id) || false;

    if (isModeratorTitle && isAlreadyMember) {
      return;
    }

    if (isMemberTitle && isAlreadyModerator) {
      return;
    }

    setSelectedUsers((prev) => {
      const isCurrentlySelected = prev.some((u) => u.id === user.id);

      if (isCurrentlySelected) {
        const updatedUsers = prev.filter((u) => u.id !== user.id);
        updateCommunityState(isModeratorTitle, updatedUsers);
        return updatedUsers;
      } else {
        const updatedUsers = [...prev, user];
        updateCommunityState(isModeratorTitle, updatedUsers);
        return updatedUsers;
      }
    });
  };

  const isUserSelected = (userId: string): boolean => {
    const isSelectedInCurrentContext = selectedUsers.some((u) => u.id === userId);

    const isSelectedAsModerator = communityState.moderators?.some((u) => u.id === userId) || false;
    const isSelectedAsMember = communityState.members?.some((u) => u.id === userId) || false;

    return isSelectedInCurrentContext || isSelectedAsModerator || isSelectedAsMember;
  };

  const isUserDisabled = (userId: string): boolean => {
    const isMemberTitle =
      title === 'Add members' || title === 'Add people' || title !== 'Add moderator';
    const isAlreadyModerator = communityState.moderators?.some((u) => u.id === userId) || false;

    return isMemberTitle && isAlreadyModerator;
  };

  const getDisplayTitle = (profile: AuthorProfileI): string => {
    const designation = profile.designation?.name || '';
    const entity = profile.entity?.name || '';

    if (designation && entity) {
      return `${designation} at ${entity}`;
    }
    return designation || entity || '';
  };

  const renderUserItem = ({ item }: { item: FetchFollowersDataI }) => {
    const { Profile } = item;
    const disabled = isUserDisabled(Profile.id);

    return (
      <TouchableOpacity
        onPress={() => !disabled && toggleUserSelection(Profile)}
        className="flex-row items-center justify-between py-3"
        disabled={disabled}
      >
        <View className={`flex-row items-center flex-1 ${disabled ? 'opacity-50' : ''}`}>
          <UserAvatar avatarUri={Profile.avatar} name={Profile.name} width={48} height={48} />{' '}
          <View className="ml-3 flex-1">
            <Text
              className={`text-base font-medium ${disabled ? 'text-gray-400' : 'text-gray-900'}`}
            >
              {Profile.name}
            </Text>
            <Text className={`text-sm mt-1 ${disabled ? 'text-gray-300' : 'text-gray-500'}`}>
              {getDisplayTitle(Profile)}
            </Text>
            {disabled && (
              <Text className="text-xs text-gray-400 mt-1">Already selected as moderator</Text>
            )}
          </View>
        </View>
        <View
          className={`w-6 h-6 rounded border-2 items-center justify-center ${
            disabled
              ? 'bg-gray-200 border-gray-200'
              : isUserSelected(Profile.id)
                ? 'bg-primaryGreen border-primaryGreen'
                : 'border-gray-300'
          }`}
        >
          {isUserSelected(Profile.id) && !disabled && (
            <Tick width={1.5} height={1.5} color="white" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderListFooter = () => {
    if (!isLoadingMore) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    );
  };

  const renderEmptyState = () => {
    if (isLoadingMore && people.length === 0) {
      return (
        <View className="items-center justify-center py-8">
          <ActivityIndicator size="large" color="#448600" />
          <Text className="text-gray-500 mt-2">Loading connections...</Text>
        </View>
      );
    }

    if (people.length === 0) {
      return (
        <View className="items-center justify-center py-8">
          <Text className="text-gray-500 text-center">
            {searchQuery ? 'No connections found matching your search' : 'No connections found'}
          </Text>
        </View>
      );
    }

    return null;
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <View className="flex-1 bg-white px-4">
      <CreateCommunityHeader
        currentPage={3}
        onNext={handleSubmit}
        buttonTitle={btnText ?? 'Create'}
        hidePageNumber={title === 'Add moderator' || title === 'Add members'}
      />
      <Text className="text-xl font-medium my-4">{title ?? 'Add people'}</Text>
      <View className="relative mb-6">
        <TextInput
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Enter name"
          className=" rounded-lg text-base"
          placeholderTextColor="#9CA3AF"
        />
      </View>

      {selectedUsers.length > 0 && (
        <View className="mb-6">
          <Text className="text-gray-500 text-sm mb-3">Selected members</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            className="flex-row"
            contentContainerStyle={{ paddingRight: 16 }}
          >
            {selectedUsers.slice(0, 5).map((user, index) => (
              <View key={user.id} className={`items-center ${index > 0 ? 'ml-4' : ''}`}>
                <View className="relative">
                  <UserAvatar avatarUri={user.avatar} name={user.name} width={48} height={48} />
                  <View className="absolute -bottom-1 -right-1 w-6 h-6 bg-primaryGreen rounded items-center justify-center">
                    <Tick width={1.5} height={1.5} color="white" />
                  </View>
                </View>
                <Text
                  className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                  numberOfLines={1}
                >
                  {user.name.split(' ')[0]}
                </Text>
              </View>
            ))}
            {selectedUsers.length > 5 && (
              <View className="items-center ml-4">
                <View className="relative">
                  <View className="w-12 h-12 rounded-full bg-gray-200 items-center justify-center">
                    <Text className="text-gray-600 font-medium">+{selectedUsers.length - 5}</Text>
                  </View>
                </View>
                <Text
                  className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                  numberOfLines={1}
                >
                  More
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      )}

      <FlatList
        data={people}
        renderItem={renderUserItem}
        keyExtractor={(item) => item.Profile.id}
        contentContainerStyle={{ paddingHorizontal: 8, paddingBottom: 24 }}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderListFooter}
        onEndReached={loadMore}
        onEndReachedThreshold={0.7}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default PeopleList;
