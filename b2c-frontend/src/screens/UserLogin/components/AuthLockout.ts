import AsyncStorage from '@react-native-async-storage/async-storage';

const LOCKOUT_KEY = 'auth_lockout';
const MAX_ATTEMPTS = 10;
const LOCKOUT_DURATION = 1 * 60 * 1000;

type LockoutData = {
  attempts: number;
  timestamp: number;
};

export const checkLockout = async (): Promise<{
  isLocked: boolean;
  remainingTime?: number;
  remainingAttempts: number;
}> => {
  try {
    const raw = await AsyncStorage.getItem(LOCKOUT_KEY);
    if (!raw) {
      return { isLocked: false, remainingAttempts: MAX_ATTEMPTS };
    }

    const { timestamp, attempts }: LockoutData = JSON.parse(raw);
    const now = Date.now();
    const elapsed = now - timestamp;

    if (elapsed >= LOCKOUT_DURATION) {
      await AsyncStorage.removeItem(LOCKOUT_KEY);
      return { isLocked: false, remainingAttempts: MAX_ATTEMPTS };
    }

    const isLocked = attempts >= MAX_ATTEMPTS;
    return {
      isLocked,
      remainingAttempts: isLocked ? 0 : MAX_ATTEMPTS - attempts,
      ...(isLocked && { remainingTime: LOCKOUT_DURATION - elapsed }),
    };
  } catch (error) {
    console.error('Error in checkLockout:', error);
    return { isLocked: false, remainingAttempts: MAX_ATTEMPTS };
  }
};

export const recordFailedAttempt = async (): Promise<{
  isLocked: boolean;
  remainingAttempts: number;
  remainingTime?: number;
}> => {
  try {
    const raw = await AsyncStorage.getItem(LOCKOUT_KEY);
    const now = Date.now();
    let attempts = 1;
    let timestamp = now;

    if (raw) {
      const parsed: LockoutData = JSON.parse(raw);
      const elapsed = now - parsed.timestamp;
      if (elapsed >= LOCKOUT_DURATION) {
        attempts = 1;
        timestamp = now;
      } else {
        attempts = parsed.attempts + 1;
        timestamp = parsed.timestamp;
      }
    }

    const isLocked = attempts >= MAX_ATTEMPTS;
    await AsyncStorage.setItem(
      LOCKOUT_KEY,
      JSON.stringify({
        attempts: isLocked ? MAX_ATTEMPTS : attempts,
        timestamp: isLocked ? now : timestamp,
      }),
    );

    return {
      isLocked,
      remainingAttempts: Math.max(0, MAX_ATTEMPTS - attempts),
      ...(isLocked && { remainingTime: LOCKOUT_DURATION }),
    };
  } catch (error) {
    console.error('Error in recordFailedAttempt:', error);
    return { isLocked: false, remainingAttempts: MAX_ATTEMPTS };
  }
};

export const resetLockout = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(LOCKOUT_KEY);
  } catch (error) {
    console.error('Error in resetLockout:', error);
  }
};
