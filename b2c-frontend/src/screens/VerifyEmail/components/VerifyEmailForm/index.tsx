import { useEffect, useRef, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@/src/components/Button';
import OtpInput from '@/src/components/OtpInput';
import { OTPInputHandle } from '@/src/components/OtpInput/types';
import { verifyEmailOTPAsync } from '@/src/redux/slices/user/userSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { sendOTPForEmailVerificationAPI } from '@/src/networks/auth/email';
import { VerifyAccountFormPropsI } from './types';

const RESEND_OTP_COUNTDOWN = 90;

export const VerifyEmailForm = ({ onSuccess, email, profileId }: VerifyAccountFormPropsI) => {
  const [otp, setOtp] = useState('');
  const [countdown, setCountdown] = useState(RESEND_OTP_COUNTDOWN);
  const [isResending, setIsResending] = useState(false);
  const otpRef = useRef<OTPInputHandle>(null);
  const dispatch = useDispatch<AppDispatch>();

  const { isEmailVerified, loading } = useSelector((state: RootState) => state.user);

  useEffect(() => {
    if (countdown <= 0) return;
    const timer = setInterval(() => setCountdown((prev) => prev - 1), 1000);
    return () => clearInterval(timer);
  }, [countdown]);

  useEffect(() => {
    if (isEmailVerified) {
      onSuccess();
    }
  }, [isEmailVerified, onSuccess]);

  const handleVerify = async () => {
    if (otp.length === 6 && profileId) {
      try {
        await dispatch(verifyEmailOTPAsync({ otp, profileId })).unwrap();
        navigate('CreateAccountSuccess');
      } catch (error) {
        showToast({
          type: 'error',
          message: 'Failed to Verify OTP',
          description: 'Please try again later.',
        });
        console.error('OTP verification failed:', error);
      }
    }
  };

  const handleResendOTP = async () => {
    if (!email || !profileId || isResending) return;

    try {
      setIsResending(true);
      await sendOTPForEmailVerificationAPI({ profileId });

      setCountdown(RESEND_OTP_COUNTDOWN);
      setOtp('');
      otpRef.current?.clear();

      showToast({
        type: 'success',
        message: 'OTP Sent',
        description: 'A new verification code has been sent to your email.',
      });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to Send OTP',
        description: 'Please try again later.',
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <View className="px-5 gap-2">
      <OtpInput
        title="Verification"
        subtitle={`Please enter the code sent to ${email}`}
        value={otp}
        onChange={setOtp}
        autoFocus
        ref={otpRef}
      />

      <View className="flex-col gap-5">
        <View className="flex-row mt-4 gap-1">
          <Text className="text-gray-500">Didn't get the code?</Text>
          <Pressable onPress={handleResendOTP} disabled={countdown > 0 || isResending}>
            <Text className={countdown > 0 || isResending ? 'text-gray-400' : 'text-green-700'}>
              {isResending
                ? 'Sending...'
                : countdown > 0
                  ? `Resend in ${countdown}s`
                  : 'Resend Code'}
            </Text>
          </Pressable>
        </View>

        <Button
          label={isEmailVerified ? 'Verified' : 'Verify'}
          onPress={handleVerify}
          variant={otp.length === 6 ? 'primary' : 'tertiary'}
          disabled={otp.length !== 6 || isEmailVerified || loading || !profileId}
          loading={loading}
        />
      </View>
    </View>
  );
};
