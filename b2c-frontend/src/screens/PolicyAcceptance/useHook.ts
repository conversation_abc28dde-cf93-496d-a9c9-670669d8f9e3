import { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { updatePrivacyPolicyAcceptance } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { BottomTabNavigationI } from '@/src/navigation/types';
import {
  acceptPrivacyPolicyAPI,
  fetchPrivacyPolicyAPI,
} from '@/src/networks/profile/privacyPolicy';
import { PPAndTNCFormDataI } from './types';

const usePolicyAcceptance = () => {
  const currentUser = useSelector(selectCurrentUser);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('privacy');
  const [privacyContent, setPrivacyContent] = useState('');
  const [termsContent, setTermsContent] = useState('');
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<BottomTabNavigationI>();

  const methods = useForm<PPAndTNCFormDataI>({
    mode: 'onChange',
    defaultValues: {
      ppAndTNC: false,
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setError,
  } = methods;

  const fetchPolicies = async () => {
    try {
      setLoading(true);

      const [privacyResult, termsResult] = await Promise.all([
        fetchPrivacyPolicyAPI({ type: 'SIGNUP_PRIVACY_POLICY' }),
        fetchPrivacyPolicyAPI({ type: 'TERMS_OF_USE' }),
      ]);

      if (privacyResult) {
        setPrivacyContent(privacyResult.content);
      }

      if (termsResult) {
        setTermsContent(termsResult.content);
      }
    } catch (error) {
      showToast({
        message: 'Error loading policies',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPolicies();
  }, []);

  const navigateBasedOnUserState = async () => {
    const { isUsernameSaved, isPersonalDetailsSaved, isWorkDetailsSaved, email } = currentUser;

    if (!isUsernameSaved) {
      navigation.navigate('SetUsername', { email: email });
    } else if (!isPersonalDetailsSaved || !isWorkDetailsSaved) {
      navigation.navigate('AddUserDetailScreen');
    }
  };

  const onSubmit = async (_data: PPAndTNCFormDataI) => {
    try {
      setIsSubmitting(true);
      const result = await acceptPrivacyPolicyAPI();

      if (result.isPrivacyPolicyAccepted) {
        dispatch(updatePrivacyPolicyAcceptance());
        navigateBasedOnUserState();
      } else {
        setError('ppAndTNC', { message: 'Failed to accept the privacy policy' });
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          setError('ppAndTNC', { message: 'Failed to accept the privacy policy' });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Server Error',
            description: 'Please try again later',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    control,
    errors,
    isSubmitting,
    onSubmit,
    setIsSubmitting,
    handleSubmit,
    isValid,
    activeTab,
    setActiveTab,
    privacyContent,
    termsContent,
    loading,
  };
};

export default usePolicyAcceptance;
