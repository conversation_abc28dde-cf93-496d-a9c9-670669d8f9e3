import { useCallback, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import { deleteEducationAPI, fetchEducationsAPI } from '@/src/networks/career/education';
import type { EducationI, UseEditEducationListI } from './types';

export const useEditEducationList = (profileId: string): UseEditEducationListI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [isVisible, setIsVisible] = useState(false);
  const [deleteEducationId, setDeleteEducationId] = useState<string>('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const [educations, setEducations] = useState<EducationI[]>([]);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchEducations = async (pageNumber: number, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const result = await fetchEducationsAPI(profileId, pageNumber, pageSize);

      if (isLoadMore) {
        setEducations((prev) => [...prev, ...result]);
      } else {
        setEducations(result);
      }

      setHasMore(result.length >= pageSize);
      setPage(pageNumber);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load educations: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchEducations(0);
    }, [profileId]),
  );

  const handleLoadMore = () => {
    if (!loadingMore && !loading && hasMore) {
      fetchEducations(page + 1, true);
    }
  };

  const onAddEducation = () => {
    navigation.navigate('EditEducationItem', { profileId });
  };

  const onEditEducation = async (educationId: string) => {
    navigation.navigate('EditEducationItem', {
      profileId,
      educationId,
    });
  };

  const onDeleteEducation = async () => {
    try {
      setIsDeleting(true);
      await deleteEducationAPI(deleteEducationId);
      setEducations((prev) => prev.filter((item) => item.id !== deleteEducationId));
      showToast({
        message: 'Success',
        description: 'Education deleted successfully',
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Delete Education',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    isSubmitting,
    onAddEducation,
    onEditEducation,
    onDeleteEducation,
    navigation,
    loading,
    loadingMore,
    hasMore,
    handleLoadMore,
    isVisible,
    setIsVisible,
    setDeleteEducationId,
    isDeleting,
    educations,
  };
};
