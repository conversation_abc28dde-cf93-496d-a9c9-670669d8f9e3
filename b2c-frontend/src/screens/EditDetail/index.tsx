import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { AppStackParamListI } from '@/src/navigation/types';
import EditShipDetailForm from './components/EditShipDetailForm';
import type { DetailScreenTypeI } from './components/EditShipDetailForm/types';

const EditDetailScreen = () => {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const route = useRoute<RouteProp<AppStackParamListI, 'EditDetail'>>();
  const { text, type, action } = route?.params;

  const handleBack = () => {
    navigation.pop();
  };

  const formComponents = {
    ship: (
      <EditShipDetailForm
        text={text as string}
        action={action}
        type={type as DetailScreenTypeI}
        handleBack={handleBack}
      />
    ),
  };

  return <SafeArea>{formComponents[type as keyof typeof formComponents]}</SafeArea>;
};

export default EditDetailScreen;
