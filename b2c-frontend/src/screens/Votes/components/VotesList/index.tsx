/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { VotesListPropsI } from './types';
import useVotesList from './useHook';

const VotesList: React.FC<VotesListPropsI> = ({ onUserPress }) => {
  const { reactions, loading } = useVotesList();

  const formattedUsers: ListItem[] = reactions.reactions?.map((reaction: any) => ({
    Profile: {
      id: reaction.Profile.id,
      avatar: reaction.Profile.avatar,
      name: reaction.Profile.name,
      designation: reaction.Profile.designation,
      entity: reaction.Profile.entity,
    },
  }));

  if (loading && formattedUsers?.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  return (
    <View className="flex-1">
      <UsersList
        data={formattedUsers}
        onPress={onUserPress}
        refreshing={false}
        loading={loading}
        onRefresh={() => {}}
        onLoadMore={() => {}}
      />
    </View>
  );
};

export default VotesList;
