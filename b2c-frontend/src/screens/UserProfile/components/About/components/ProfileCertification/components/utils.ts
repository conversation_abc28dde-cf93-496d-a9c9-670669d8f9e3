export const expiresIn = (date: string | null) => {
  if (date === null || date === undefined) {
    return ['Unlimited'];
  }
  const today = new Date();
  const validityDate = new Date(date);

  if (validityDate > today) {
    const yearDiff = validityDate.getFullYear() - today.getFullYear();
    const monthDiff = validityDate.getMonth() - today.getMonth();
    let totalMonths = yearDiff * 12 + monthDiff;

    const diffTime = validityDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return totalMonths >= 1 ? [totalMonths, 'M'] : [diffDays, 'D'];
  } else {
    return [];
  }
};
