/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import {
  selectForumCommentReplies,
  selectForumAnswerCommentReplies,
} from '@/src/redux/selectors/forum';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  deleteForumComment,
  fetchCommentReplies,
  deleteForumAnswerComment,
  fetchAnswerCommentReplies,
} from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { ForumCommentTypeI, ForumCommentReplyTypeI } from '../types';

const CommentItem: React.FC<{
  comment: ForumCommentTypeI;
  onReply?: () => void;
  postId?: string;
  type: 'FORUM_QUESTION' | 'FORUM_ANSWER';
}> = ({ comment, onReply, postId, type }) => {
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const { id, text, replies: initialReplies, replyCount } = comment;

  const profile = 'profile' in comment ? comment.profile : comment.Profile;

  const [showReplies, setShowReplies] = useState(false);
  const [modalState, setModalState] = useState<{
    isVisible: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
  }>({
    isVisible: false,
    title: '',
    description: '',
    onConfirm: () => {},
  });

  const {
    comments: fetchedReplies,
    nextCursorId,
    total,
  } = useSelector((state: RootState) =>
    type === 'FORUM_QUESTION'
      ? selectForumCommentReplies(state, id)
      : selectForumAnswerCommentReplies(state, id),
  );
  const loading = useSelector((state: RootState) => state.forum.answerLoading);

  const handleShowReplies = () => {
    if (!showReplies) {
      setShowReplies(true);
      if (fetchedReplies.length === 0) {
        if (type === 'FORUM_QUESTION') {
          dispatch(
            fetchCommentReplies({
              questionId: postId!,
              parentCommentId: id,
              cursorId: null,
              pageSize: 10,
            }),
          );
        } else {
          dispatch(
            fetchAnswerCommentReplies({
              answerId: postId!,
              parentCommentId: id,
              cursorId: null,
              pageSize: 10,
            }),
          );
        }
      }
    }
  };

  const loadMore = () => {
    if (fetchedReplies.length < total) {
      if (type === 'FORUM_QUESTION') {
        dispatch(
          fetchCommentReplies({
            questionId: postId!,
            parentCommentId: id,
            cursorId: nextCursorId,
            pageSize: 10,
          }),
        );
      } else {
        dispatch(
          fetchAnswerCommentReplies({
            answerId: postId!,
            parentCommentId: id,
            cursorId: nextCursorId,
            pageSize: 10,
          }),
        );
      }
    }
  };

  const allReplies =
    showReplies && fetchedReplies.length > 0 ? fetchedReplies : initialReplies || [];
  const hasMoreReplies = allReplies.length < (comment.replyCount || 0);

  const handleDeleteComment = () => {
    if (type === 'FORUM_QUESTION') {
      dispatch(deleteForumComment({ commentId: id, questionId: postId! }));
    } else {
      dispatch(deleteForumAnswerComment({ commentId: id, answerId: postId! }));
    }
    setModalState({ ...modalState, isVisible: false });
  };

  const handleDeleteReply = (replyId: string) => {
    if (type === 'FORUM_QUESTION') {
      dispatch(
        deleteForumComment({
          commentId: replyId,
          questionId: postId!,
          parentCommentId: comment.id,
        }),
      );
    } else {
      dispatch(
        deleteForumAnswerComment({
          commentId: replyId,
          answerId: postId!,
          parentCommentId: comment.id,
        }),
      );
    }
    setModalState({ ...modalState, isVisible: false });
  };

  return (
    <>
      <View className="bg-white overflow-hidden p-2 border-b border-gray-200 rounded-lg">
        <View className=" flex-row items-center">
          <Text className="text-base font-normal text-[#6E6E6E]">
            {text}
            <Text className="text-base font-medium text-[#6E6E6E]">
              {' - @'}
              {profile ? profile.name : 'Anonymous'}
            </Text>
          </Text>
        </View>
        <View className="p-2 flex-row items-center gap-4">
          <Pressable onPress={onReply}>
            <Text className="text-subLabelGray text-xs">Reply</Text>
          </Pressable>
          {currentUser.profileId === profile?.id && (
            <Pressable
              onPress={() =>
                setModalState({
                  isVisible: true,
                  title: 'Delete Comment',
                  description: 'Are you sure you want to delete this comment?',
                  onConfirm: handleDeleteComment,
                })
              }
            >
              <Text className="text-red-500 text-xs">Delete</Text>
            </Pressable>
          )}

          {(replyCount > 0 || allReplies.length > 0) && (
            <Pressable onPress={handleShowReplies}>
              <Text className="text-blue-500 text-xs">
                {showReplies
                  ? ''
                  : (() => {
                      const count = replyCount ?? allReplies.length;
                      return `Show ${count === 1 ? 'reply' : 'replies'}`;
                    })()}
              </Text>
            </Pressable>
          )}
        </View>
        {showReplies && (
          <View>
            {allReplies.map((reply: ForumCommentReplyTypeI) => {
              const replyProfile = 'profile' in reply ? reply.profile : reply.Profile;
              return (
                <View key={reply.id} className="flex-col items-start flex-wrap p-2 ml-4">
                  <Text className="text-base font-normal text-[#6E6E6E] px-2">
                    {reply.text ?? '-'}
                    <Text className="text-base font-medium text-[#6E6E6E]">
                      {' - @'}
                      {replyProfile ? replyProfile.name : 'Anonymous'}
                    </Text>
                  </Text>
                  {currentUser.profileId === replyProfile?.id && (
                    <View className="px-2 py-1">
                      <Pressable
                        onPress={() => {
                          setModalState({
                            isVisible: true,
                            title: 'Delete Reply',
                            description: 'Are you sure you want to delete this reply?',
                            onConfirm: () => handleDeleteReply(reply.id),
                          });
                        }}
                      >
                        <Text className="text-red-500 text-xs">Delete</Text>
                      </Pressable>
                    </View>
                  )}
                </View>
              );
            })}
            {hasMoreReplies && !loading && (
              <Pressable onPress={loadMore} className="p-2 ml-4">
                <Text className="text-blue-500 text-xs">Show more replies</Text>
              </Pressable>
            )}
            {loading && <Text className="p-2 ml-4 text-xs">Loading...</Text>}
          </View>
        )}
      </View>
      <CustomModal
        isVisible={modalState.isVisible}
        title={modalState.title}
        description={modalState.description}
        onConfirm={modalState.onConfirm}
        onCancel={() => setModalState({ ...modalState, isVisible: false })}
        confirmButtonVariant="danger"
      />
    </>
  );
};

export default CommentItem;
