/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type ForumCommentReplyProps = {
  replyId: string;
  commentId: string;
  userId: string;
  content: string;
};

export type ForumCommentProps = {
  commentId: string;
  commentsType: 'postComment' | 'answersComment';
  postId?: string;
  answerId?: string;
  userId: string;
  content: string;
  replies: ForumCommentReplyProps[];
};
