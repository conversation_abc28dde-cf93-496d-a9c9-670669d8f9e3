/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useRef, useState, useEffect } from 'react';
import {
  Keyboard,
  TextInput,
  View,
  Pressable,
  Text,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { createForumComment, createForumAnswerComment } from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch } from '@/src/redux/store';
import Send from '@/src/assets/svgs/Send';
import { QuestionCommentI } from '@/src/networks/forum/types';
import CommentPreview from '../../Comment/components/CommentPreview';
import { ForumCommentTypeI } from '../types';

const MAX_COMMENT_LENGTH = 255;

type Props = {
  postId: string;
  type: 'FORUM_QUESTION' | 'FORUM_ANSWER';
  parentCommentId?: string;
  replyPreview: ForumCommentTypeI | null;
  setReplyPreview: (comment: ForumCommentTypeI | null) => void;
  onCommentSuccess?: (comment: QuestionCommentI) => void;
};

const CommentInput = ({
  postId,
  type,
  parentCommentId,
  replyPreview,
  setReplyPreview,
  onCommentSuccess,
}: Props) => {
  const dispatch = useDispatch<AppDispatch>();
  const [comment, setComment] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUser = useSelector(selectCurrentUser);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(e.endCoordinates.height);
      }
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(0);
      }
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (replyPreview && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [replyPreview]);

  const handleClose = () => {
    setReplyPreview(null);
    setComment('');
    Keyboard.dismiss();
  };

  const handleCommentText = (text: string) => {
    if (text.length <= MAX_COMMENT_LENGTH) {
      setComment(text);
    }
  };

  const isReply = Boolean(parentCommentId && replyPreview);
  const canSubmit = comment.trim().length > 0 && !isSubmitting;
  const isNearLimit = comment.length > MAX_COMMENT_LENGTH * 0.8;

  const handleSend = async () => {
    if (canSubmit) {
      setIsSubmitting(true);
      if (type === 'FORUM_QUESTION') {
        dispatch(
          createForumComment({
            questionId: postId,
            text: comment.trim(),
            parentCommentId,
          }),
        )
          .then(() => {
            setComment('');
            setReplyPreview(null);
            Keyboard.dismiss();
          })
          .finally(() => {
            setIsSubmitting(false);
          });
      } else {
        dispatch(
          createForumAnswerComment({
            answerId: postId,
            text: comment.trim(),
            parentCommentId,
          }),
        )
          .then(() => {
            setComment('');
            setReplyPreview(null);
            setComment('');
            setReplyPreview(null);
            Keyboard.dismiss();
          })
          .finally(() => {
            setIsSubmitting(false);
          });
      }
    }
  };

  const isAndroid15Plus = Platform.OS === 'android' && Number(Platform.Version) >= 35;
  const paddingBottom =
    isAndroid15Plus && keyboardHeight > 0 ? Math.max(keyboardHeight - 50, 0) : 0;
  const marginBottom = isAndroid15Plus && keyboardHeight > 0 ? 40 : 0;

  const profile = replyPreview
    ? 'profile' in replyPreview
      ? replyPreview.profile
      : replyPreview.Profile
    : null;

  return (
    <View
      className="bg-white border-t border-gray-200"
      style={{
        paddingBottom: paddingBottom,
        marginBottom: marginBottom,
      }}
    >
      {replyPreview && (
        <View className="bg-gray-50 border-b border-gray-200 p-3">
          <View className="flex-row justify-between items-start">
            <Text className="text-xs text-gray-600 font-medium mb-2">Replying to</Text>
            <Pressable onPress={handleClose}>
              <Text className="text-gray-400 text-lg">×</Text>
            </Pressable>
          </View>

          <View className="flex-row gap-2">
            {/* <UserAvatar avatarUri={comment.Profile?.avatar} height={24} width={24} /> */}
            <View className="flex-1">
              <Text className="text-xs font-medium text-gray-700">
                @{profile ? profile.name : 'Anonymous'}
              </Text>
              <Text className="text-xs text-gray-600 mt-1" numberOfLines={2}>
                {replyPreview.text}
              </Text>
            </View>
          </View>
        </View>
      )}

      <View className="p-4">
        <View className="flex-row items-start gap-3">
          {/* <UserAvatar avatarUri={user.avatar} height={40} width={40} /> */}

          <View className="flex-1">
            <View className="relative">
              <TextInput
                ref={inputRef}
                className="bg-gray-100 rounded-2xl text-black px-4 py-3 pr-12 text-base min-h-[44px] max-h-[120px]"
                placeholder={isReply ? `Reply to ${'add here'}...` : 'Write a comment...'}
                placeholderTextColor="#9CA3AF"
                value={comment}
                onChangeText={handleCommentText}
                maxLength={MAX_COMMENT_LENGTH}
                multiline
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                autoCorrect
                editable={!isSubmitting}
                textAlignVertical="top"
              />
              <Pressable
                onPress={handleSend}
                disabled={!canSubmit}
                className={`absolute right-2 bottom-2 w-8 h-8 rounded-full items-center justify-center ${
                  canSubmit ? 'bg-green-800' : 'bg-gray-300'
                }`}
              >
                {isSubmitting ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Send width={1.6} height={1.6} color="white" strokeWidth={2} />
                )}
              </Pressable>
            </View>

            {(isFocused || isNearLimit) && (
              <View className="flex-row justify-between items-center mt-2 px-2">
                <Text className={`text-xs ${isNearLimit ? 'text-orange-500' : 'text-gray-500'}`}>
                  {comment.length}/{MAX_COMMENT_LENGTH}
                </Text>
                {isReply && (
                  <Pressable onPress={handleClose}>
                    <Text className="text-xs text-green-800 font-medium">Cancel Reply</Text>
                  </Pressable>
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default CommentInput;
