/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type ForumCommentInputProps = {
  userId: string;
  postId?: string;
  answerId?: string;
  type: 'answersComment' | 'postComment';
  parentCommentId?: string;
  replyPreview?: {
    commentId: string;
    content: string;
    userId: string;
  } | null;
  setReplyPreview: React.Dispatch<
    React.SetStateAction<{
      commentId: string;
      content: string;
      userId: string;
    } | null>
  >;
};
