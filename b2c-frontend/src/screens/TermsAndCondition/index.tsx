import { View, Text, ScrollView, ActivityIndicator, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Markdown from 'react-native-markdown-display';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { useTermsAndConditions } from './useHook';

const TermsAndConditionsScreen = () => {
  const navigation = useNavigation();
  const { termsContent, loading } = useTermsAndConditions();

  const handleBack = () => {
    navigation.goBack();
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="small" color="#448600" />
        </View>
      </SafeArea>
    );
  }

  const cleanContent = termsContent
    ?.replace(/\\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\'/g, "'");

  const markdownStyles = StyleSheet.create({
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: '#374151',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
      marginTop: 24,
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 12,
      marginTop: 20,
    },
    heading3: {
      fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
      marginTop: 16,
    },
    paragraph: {
      marginBottom: 12,
      lineHeight: 22,
    },
    list_item: {
      marginBottom: 8,
    },
    bullet_list: {
      marginBottom: 16,
    },
    ordered_list: {
      marginBottom: 16,
    },
    strong: {
      fontWeight: 'bold',
    },
    em: {
      fontStyle: 'italic',
    },
    link: {
      color: '#004687',
      textDecorationLine: 'underline',
    },
  });

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 py-2">
          <BackButton onBack={handleBack} label="" />
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 pb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-6">Terms & Conditions</Text>

            {cleanContent ? <Markdown style={markdownStyles}>{cleanContent}</Markdown> : null}
          </View>
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default TermsAndConditionsScreen;
