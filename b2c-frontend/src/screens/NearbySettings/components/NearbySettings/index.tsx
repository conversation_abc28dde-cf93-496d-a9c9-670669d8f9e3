import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import TextInput from '@/src/components/TextInput';
import AddItem from '@/src/assets/svgs/AddItem';
import TrashBin from '@/src/assets/svgs/TrashBin';
import InformationText from '../InformationText';
import { EditNearbySettingsPropsI } from './types';
import { useNearbySettings } from './useHook';

const EditNearbySettings = ({ onBack }: EditNearbySettingsPropsI) => {
  const {
    hasChanges,
    methods,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
  } = useNearbySettings();

  const { control } = methods;

  if (loading) {
    return <ActivityIndicator />;
  }

  return (
    <ScrollView className="flex-1 bg-white px-4" showsVerticalScrollIndicator={false}>
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Nearby Settings" />
        <Pressable onPress={() => {}} disabled={!hasChanges}>
          <Text
            className={`text-lg font-medium ${!hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
          >
            Apply
          </Text>
        </Pressable>
      </View>
      <View className="gap-4">
        <InformationText />
        <Controller
          control={control}
          name="selfLocation"
          rules={{ required: 'Location is required' }}
          render={({ fieldState: { error } }) => <TextInput label="Location" />}
        />
        <Pressable onPress={handleAddOtherLocation}>
          <AddItem />
        </Pressable>
        {isOtherLocationAdded && (
          <View>
            <Controller
              control={control}
              name="otherLocation"
              render={({ fieldState: { error } }) => <TextInput />}
            />
            <View className="flex-row justify-between">
              <View></View>
              <Pressable className="m-3" onPress={handleDeleteOtherLocation}>
                <TrashBin width={2.5} height={2.5} color="red" />
              </Pressable>
            </View>
          </View>
        )}
        <Controller
          control={control}
          name="radius"
          rules={{ required: 'Radius is required' }}
          render={({ fieldState: { error } }) => <TextInput label="Radius(km)" />}
        />
      </View>
    </ScrollView>
  );
};

export default EditNearbySettings;
