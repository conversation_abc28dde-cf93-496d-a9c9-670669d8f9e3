import { useCallback, useEffect, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import { useFieldArray, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import {
  clearOtherLocation,
  setOtherLocation,
  setSelfLocation,
} from '@/src/redux/slices/announcement/announcementSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { NearbySettingsFormFiltersI } from './types';

export const useNearbySettings = () => {
  const isOtherLocationAdded = Boolean(useSelector(selectNearbyFilters).otherLocation.length);
  const dispatch = useDispatch<AppDispatch>();
  const [selfLocationSuggestions, setSelfLocationSuggestions] = useState<any[]>([]);
  const [otherLocationSuggestions, setOtherLocationSuggestions] = useState<any[]>([]);

  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  // Mapbox Geocoding API call
  const fetchLocationSuggestions = async (query: string) => {
    try {
      setIsFetching(true);
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=YOUR_MAPBOX_ACCESS_TOKEN`,
      );
      const data = await response.json();
      return data.features || [];
    } catch (error) {
      console.error('Geocoding error:', error);
      return [];
    } finally {
      setIsFetching(false);
    }
  };

  // const debouncedSearch = useCallback(
  //     debounce(async (query: string, setSuggestions: React.Dispatch<React.SetStateAction<any[]>>) => {
  //         if (query.length > 2) {
  //             const results = await fetchLocationSuggestions(query);
  //             setSuggestions(results);
  //         } else {
  //             setSuggestions([]);
  //         }
  //     }, 4000), // 4 second debounce
  //     []
  // );

  const methods = useForm<NearbySettingsFormFiltersI>({
    mode: 'onChange',
    defaultValues: {
      selfLocation: '',
      otherLocation: '',
      radius: 0,
    },
  });

  useEffect(() => {
    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          setLoading(true);
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          }
        } catch (err) {
          showToast({
            type: 'error',
            message: 'Error.Try after sometime',
          });
        } finally {
          setLoading(false);
        }
      } else {
        getCurrentLocation();
      }
    };

    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        async (position) => {
          const response = await fetch(
            'https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=YOUR_MAPBOX_ACCESS_TOKEN',
          );
          const place_name = await response.json();
          methods.setValue('selfLocation', place_name, { shouldDirty: true });
          dispatch(setSelfLocation([position.coords.longitude, position.coords.latitude]));
        },
        (err) => {
          showToast({
            type: 'error',
            message: err.message,
          });
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    requestLocationPermission();
  }, []);

  const handleAddOtherLocation = () => {
    dispatch(setOtherLocation([0, 0]));
  };

  const handleDeleteOtherLocation = () => {
    dispatch(clearOtherLocation());
  };

  const handleSelfLocationChange = (text: string) => {
    methods.setValue('selfLocation', text);
    // debouncedSearch(text, setSelfLocationSuggestions);
    fetchLocationSuggestions(text);
  };

  // Handler for other location input change
  const handleOtherLocationChange = (text: string) => {
    methods.setValue('otherLocation', text);
    // debouncedSearch(text, setOtherLocationSuggestions);
    fetchLocationSuggestions(text);
  };

  // Handler for selecting a suggestion
  const handleSelectSuggestion = (suggestion: any, fieldName: 'selfLocation' | 'otherLocation') => {
    const [longitude, latitude] = suggestion.center;
    methods.setValue(fieldName, suggestion.place_name);

    if (fieldName === 'selfLocation') {
      setSelfLocationSuggestions([]);
      dispatch(setSelfLocation([longitude, latitude]));
    } else {
      setOtherLocationSuggestions([]);
      dispatch(setOtherLocation([longitude, latitude]));
    }
  };

  const hasChanges = methods.formState.isDirty;

  return {
    methods,
    hasChanges,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
  };
};
