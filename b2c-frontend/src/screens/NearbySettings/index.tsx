/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import EditNearbySettings from './components/NearbySettings';

export const NearbySettingsScreen = () => {
  const navigation = useNavigation();

  return (
    <SafeArea>
      <EditNearbySettings onBack={navigation.goBack} />
    </SafeArea>
  );
};
