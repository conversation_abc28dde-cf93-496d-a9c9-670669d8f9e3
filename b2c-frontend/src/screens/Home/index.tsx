/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect, useState } from 'react';
import { StatusBar, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser, selectPreviousStatus } from '@/src/redux/selectors/user';
import { clearPreviousStatus, fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { BottomTabNavigationI, HomeScreenActionsRef } from '@/src/navigation/types';
import UserPostList from './components/PostList';
import TopBar from './components/TopBar';

const getModalContent = (
  status?: 'INACTIVE' | 'SCHEDULED_FOR_DELETION' | 'ACTIVE' | 'BLOCKED' | 'DELETED',
) => {
  switch (status) {
    case 'INACTIVE':
      return {
        title: 'Welcome Back',
        description: 'Your account was inactive and has been reactivated.',
      };
    case 'SCHEDULED_FOR_DELETION':
      return {
        title: 'Account Restored',
        description: 'Your account was scheduled for deletion but has been restored successfully.',
      };
    default:
      return {
        title: 'Welcome Back',
        description: 'Your account status has been updated.',
      };
  }
};

const HomeScreen: React.FC<{ homeScreenActionsRef?: HomeScreenActionsRef }> = ({
  homeScreenActionsRef,
}) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const previousStatus = useSelector(selectPreviousStatus);
  const [isInactiveModalVisible, setIsInactiveModalVisible] = useState(false);

  useEffect(() => {
    if (previousStatus === 'INACTIVE' || previousStatus === 'SCHEDULED_FOR_DELETION') {
      setIsInactiveModalVisible(true);
    }
  }, [previousStatus]);

  const handleSearchPress = () => {
    navigation.navigate('HomeStack', {
      screen: 'GlobalSearch',
    });
  };
  const handleMessagePress = () => {
    navigation.navigate('HomeStack', {
      screen: 'Chats',
      params: { profileId: currentUser.profileId },
    });
  };
  const handleNearbyPress = () => {
    navigation.navigate('HomeStack', {
      screen: 'Nearby',
    });
  };

  const handleLeaderBoardPress = () => {
    navigation.navigate('HomeStack', {
      screen: 'Leaderboard',
    });
  }

  const fetchCurrentUserProfile = async () => {
    try {
      if (currentUser?.profileId) {
        await dispatch(fetchAndSaveUserProfile({ id: currentUser.profileId }));
      }
    } catch (err) {
      handleError(err);
    }
  };

  useEffect(() => {
    fetchCurrentUserProfile();
  }, []);

  const handleConfirmInactive = () => {
    setIsInactiveModalVisible(false);
    dispatch(clearPreviousStatus());
  };

  const { description, title } = getModalContent(previousStatus);

  return (
    <SafeArea>
      <CustomModal
        isVisible={isInactiveModalVisible}
        title={title}
        description={description}
        confirmText="OK"
        onConfirm={handleConfirmInactive}
        onCancel={handleConfirmInactive}
      />
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="flex-1">
        <TopBar
          onSearchPress={handleSearchPress}
          onMessagePress={handleMessagePress}
          onNearbyPress={handleNearbyPress}
          onLeaderBoardPress={handleLeaderBoardPress}
        />
        <UserPostList
          setScrollToTop={(fn) => {
            if (homeScreenActionsRef?.current) homeScreenActionsRef.current.scrollToTop = fn;
          }}
          setHandleRefresh={(fn) => {
            if (homeScreenActionsRef?.current) homeScreenActionsRef.current.handleRefresh = fn;
          }}
        />
      </View>
    </SafeArea>
  );
};

export default HomeScreen;
