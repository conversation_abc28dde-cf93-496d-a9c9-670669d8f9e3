import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI, LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateQuestionForm from './components/CreateQuestionForm';

const CreateQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleSuccess = () => {
    navigation.navigate('Forum');
  };

  return (
    <SafeArea>
      <CreateQuestionForm onSuccess={handleSuccess} />
    </SafeArea>
  );
};

export default CreateQuestionScreen;
