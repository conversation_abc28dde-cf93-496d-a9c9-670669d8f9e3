/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ListItem } from '@/src/components/UsersList/types';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

export type BlockedListNavigationI = StackNavigationProp<LearnCollabStackParamsListI>;
export type BlockedListRouteI = RouteProp<LearnCollabStackParamsListI, 'CommunityBlocked'>;

export type BlockedListPropsI = {
  forumId: string;
};

export type UseBlockedListPropsI = {
  forumId: string;
  pageSize?: number;
};

export type BlockedListHookResultI = {
  data: ListItem[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  error: Error | null;
  loadMore: () => void;
  refresh: () => void;
  handleUnblock: (profileId: string) => Promise<void>;
  isUnblocking: Record<string, boolean>;
};

export type ForumBlockedUserI = {
  id: string;
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designation: {
      id: string;
      name: string;
    } | null;
    entity: {
      id: string;
      name: string;
    } | null;
  };
  blockedAt?: string;
  blockedBy?: string;
  reason?: string;
};
