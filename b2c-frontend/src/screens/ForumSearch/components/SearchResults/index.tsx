/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useCallback } from 'react';
import { ActivityIndicator, Pressable, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { FlatList, RefreshControl } from 'react-native-gesture-handler';
import { useDispatch } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import type { AppDispatch } from '@/src/redux/store';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { GlobalSearchQuestionItemI, GlobalSearchCommunityItemI } from '@/src/networks/forum/types';
import { transformSearchQuestionToForumPost } from '../../utils/transformSearchToForumPost';
import { ForumSearchType } from '../SearchBox/types';
import SearchCommunityItem from '../SearchCommunityItem';
import type { SearchResultsProps } from './types';

const ListFooter = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;
  return (
    <View className="py-4">
      <ActivityIndicator size="small" />
    </View>
  );
};

const SearchResults: React.FC<SearchResultsProps & ForumSearchType> = ({
  loading,
  searchResults,
  onRefresh,
  refreshing,
  activeTab,
  searchText,
  onLoadMore,
  hasMore,
}) => {
  const data = searchResults?.data;
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  const handleForumPress = useCallback(
    async (post: { id: string }) => {
      dispatch(fetchForumQuestionDetail({ questionId: post.id }));
      navigation.navigate('ForumAnswers', { postId: post.id });
    },
    [dispatch, navigation],
  );

  const handleEndReached = () => {
    if (hasMore && !loading && onLoadMore) {
      onLoadMore();
    }
  };

  if (loading && (!data || data.length === 0)) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (!data || data.length === 0) {
    return (
      <View className="flex-1 justify-center">
        <NotFound title="No Results Found" subtitle="Try searching for a different keyword" />
      </View>
    );
  }

  return (
    <View className="flex-1">
      {activeTab === 'posts' ? (
        <FlatList
          data={data as GlobalSearchQuestionItemI[]}
          keyExtractor={(item) => (item as GlobalSearchQuestionItemI).id}
          renderItem={({ item }) => {
            const forumPostProps = transformSearchQuestionToForumPost(
              item as GlobalSearchQuestionItemI,
            );
            return (
              <Pressable onPress={() => handleForumPress(item)}>
                <ForumPost post={{ ...forumPostProps, highlightText: searchText }} />
              </Pressable>
            );
          }}
          refreshControl={
            <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={<ListFooter isLoading={loading && (hasMore || false)} />}
        />
      ) : (
        <FlatList
          data={data as GlobalSearchCommunityItemI[]}
          keyExtractor={(item) => (item as GlobalSearchCommunityItemI).id}
          renderItem={({ item }) => (
            <SearchCommunityItem community={item as GlobalSearchCommunityItemI} />
          )}
          refreshControl={
            <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: 'white',
            paddingVertical: 20,
          }}
        />
      )}
    </View>
  );
};

export default SearchResults;
