/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { GlobalSearchQuestionItemI, GlobalSearchCommunityItemI } from '@/src/networks/forum/types';

export type ForumSearchCategory = 'posts' | 'communities';

export type ForumSearchCategoryTabs = {
  id: ForumSearchCategory;
  label: string;
};

export type ForumSearchResponse = {
  data: GlobalSearchQuestionItemI[] | GlobalSearchCommunityItemI[];
  total: number;
};

export type SearchResultsProps = {
  searchResults: ForumSearchResponse;
  loading: boolean;
  refreshing: boolean;
  activeTab: ForumSearchCategory;
  onRefresh: () => void;
  searchText?: string;
  onLoadMore?: () => void;
  hasMore?: boolean;
};
