import { View, Text, Image, StyleSheet } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import type { MapAvatarProps } from './types';

const MapAvatar = ({ uri, name, profileId, onImageLoaded }: MapAvatarProps) => {
  const handleLoadEnd = () => {
    if (profileId && onImageLoaded) {
      onImageLoaded(profileId);
    }
  };

  const handleError = () => {
    if (profileId && onImageLoaded) {
      onImageLoaded(profileId);
    }
  };

  const hasValidImageUri =
    (typeof uri === 'string' && uri !== '') ||
    (typeof uri === 'object' && uri !== null && 'uri' in uri);

  const imageSource = typeof uri === 'string' ? { uri } : uri;

  return (
    <View style={styles.container} collapsable={false}>
      {hasValidImageUri ? (
        <>
          <Image
            source={{ uri: imageSource?.uri }}
            style={styles.image}
            onLoadEnd={handleLoadEnd}
            onError={handleError}
          />
        </>
      ) : (
        <UserAvatar avatarUri={null} name={name} width={40} height={40} />
      )}
      {name && <Text style={styles.name}>{name}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: 60,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'white',
  },
  loadingOverlay: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  name: {
    marginTop: 4,
    fontSize: 10,
    fontWeight: 'bold',
    backgroundColor: 'white',
    paddingHorizontal: 4,
    borderRadius: 4,
    zIndex: 1,
  },
});

export default MapAvatar;
