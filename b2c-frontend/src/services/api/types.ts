/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { PlatformOSType } from 'react-native';
import { ObjStrI, ObjUnknownI } from '@/src/types/common/data';

export interface APICallI<PayloadT = unknown> {
  payload?: PayloadT;
  query?: ObjUnknownI;
  routeId?: string | number;
  isAuth: boolean;
  headers?: ObjStrI;
}

export interface AuthHeaderI {
  Authorization?: string;
}

export interface HeadersBase extends AuthHeaderI {
  'Content-Type'?: string;
  Accept?: string;
  'x-api-key': string;
  'x-device-id': string;
  'x-platform': PlatformOSType;
  'x-version-no': string;
}

export type HeadersI = HeadersBase & ObjStrI;

export type MethodI = 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';
