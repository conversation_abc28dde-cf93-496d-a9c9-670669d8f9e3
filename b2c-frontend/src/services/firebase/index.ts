import { Platform } from 'react-native';
import Config from 'react-native-config';
import { initializeApp, getApp, ReactNativeFirebase, getApps } from '@react-native-firebase/app';
import { FirebaseMessagingTypes, getMessaging } from '@react-native-firebase/messaging';

class FirebaseService {
  private static instance: FirebaseService;
  public static app: ReactNativeFirebase.FirebaseApp;
  public static messaging: FirebaseMessagingTypes.Module;

  private constructor() {}
  public static async getInstance(): Promise<FirebaseService> {
    if (!FirebaseService.instance) {
      FirebaseService.instance = new FirebaseService();
      if (!this.app) {
        const appId =
          Platform.OS === 'ios' ? Config.FIREBASE_IOS_APP_ID : Config.FIREBASE_ANDROID_APP_ID;
        this.app = getApps().length
          ? getApp()
          : await initializeApp({
              apiKey: Config.FIREBASE_API_KEY,
              authDomain: Config.FIREBASE_AUTH_DOMAIN,
              databaseURL: '',
              projectId: Config.FIREBASE_PROJECT_ID,
              storageBucket: Config.FIREBASE_STORAGE_BUCKET,
              messagingSenderId: Config.FIREBASE_MESSAGING_SENDER_ID,
              appId,
            });
        if (!this.messaging) {
          this.messaging = getMessaging(this.app);
          return new FirebaseService();
        }
        return new FirebaseService();
      }
    }

    return FirebaseService.instance;
  }
}
export default FirebaseService;
