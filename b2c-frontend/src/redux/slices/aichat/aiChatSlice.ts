/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Message, AIChatState } from './types';

const initialState: AIChatState = { messages: [] };

const aiChatSlice = createSlice({
  name: 'aichat',
  initialState,
  reducers: {
    addMessage(state, action: PayloadAction<Message>) {
      state.messages.push(action.payload);
    },
    updateMessage(state, action: PayloadAction<{ id: number; text: string }>) {
      const msg = state.messages.find((m) => m.id === action.payload.id);
      if (msg) msg.text = action.payload.text;
    },
    clearChat(state) {
      state.messages = [];
    },
    removeOldMessages(state, action: PayloadAction<number>) {
      const now = Date.now();
      state.messages = state.messages.filter(
        (msg) => now - new Date(msg.timestamp).getTime() <= action.payload,
      );
    },
  },
});

export const { addMessage, updateMessage, clearChat, removeOldMessages } = aiChatSlice.actions;
export default aiChatSlice.reducer;
