/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ImageSourcePropType } from 'react-native';

export type Message = {
  id: number;
  text: string;
  from: 'ai' | 'me';
  user: {
    id: string;
    name: string;
    avatar?: ImageSourcePropType;
  };
  timestamp: string;
};
export type AIChatState = {
  messages: Message[];
};
