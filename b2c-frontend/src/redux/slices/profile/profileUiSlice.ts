/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ProfileTabId, ProfileUiState } from './types';

const initialState: ProfileUiState = {
  activeTab: 'about',
};

const profileUiSlice = createSlice({
  name: 'profileUi',
  initialState,
  reducers: {
    setActiveTab(state, action: PayloadAction<ProfileTabId>) {
      state.activeTab = action.payload;
    },
    resetProfileUi(state) {
      state.activeTab = 'about';
    },
  },
});

export const { setActiveTab, resetProfileUi } = profileUiSlice.actions;
export default profileUiSlice.reducer;
