/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import { showToast } from '@/src/utilities/toast';
import type { VoteTypeE } from '@/src/networks/forum/types';
import {
  createQuestionVoteAPI,
  deleteQuestionVoteAPI,
  createAnswerVoteAPI,
  deleteAnswerVoteAPI,
} from '@/src/networks/forum/vote';
import type {
  VoteStateI,
  VoteQuestionPayloadI,
  VoteAnswerPayloadI,
  RemoveQuestionVotePayloadI,
  RemoveAnswerVotePayloadI,
} from './types';

const initialState: VoteStateI = {
  questionVotes: {},
  answerVotes: {},
  questionVoteLoading: {},
  answerVoteLoading: {},
  error: null,
};

export const voteQuestion = createAsyncThunk(
  'vote/voteQuestion',
  async ({ questionId, type }: VoteQuestionPayloadI, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { vote: VoteStateI };
      const currentVote = state.vote.questionVotes[questionId];

      if (currentVote === type) {
        await deleteQuestionVoteAPI({ questionId });
        return { questionId, type: null };
      }

      await createQuestionVoteAPI({ questionId, type });
      return { questionId, type };
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
      return rejectWithValue('Failed to vote on question');
    }
  },
);

export const removeQuestionVote = createAsyncThunk(
  'vote/removeQuestionVote',
  async ({ questionId }: RemoveQuestionVotePayloadI, { rejectWithValue }) => {
    try {
      await deleteQuestionVoteAPI({ questionId });
      return { questionId };
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Remove Vote Failed',
        description: 'Failed to remove your vote. Please try again.',
      });
      return rejectWithValue('Failed to remove vote from question');
    }
  },
);

export const voteAnswer = createAsyncThunk(
  'vote/voteAnswer',
  async ({ answerId, type }: VoteAnswerPayloadI, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { vote: VoteStateI };
      const currentVote = state.vote.answerVotes[answerId];

      if (currentVote === type) {
        await deleteAnswerVoteAPI({ answerId });
        return { answerId, type: null };
      }

      // Create or update vote
      await createAnswerVoteAPI({ answerId, type });
      return { answerId, type };
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
      return rejectWithValue('Failed to vote on answer');
    }
  },
);

export const removeAnswerVote = createAsyncThunk(
  'vote/removeAnswerVote',
  async ({ answerId }: RemoveAnswerVotePayloadI, { rejectWithValue }) => {
    try {
      await deleteAnswerVoteAPI({ answerId });
      return { answerId };
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Remove Vote Failed',
        description: 'Failed to remove your vote. Please try again.',
      });
      return rejectWithValue('Failed to remove vote from answer');
    }
  },
);

const voteSlice = createSlice({
  name: 'vote',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setQuestionVote: (
      state,
      action: PayloadAction<{ questionId: string; type: VoteTypeE | null }>,
    ) => {
      const { questionId, type } = action.payload;
      state.questionVotes[questionId] = type;
    },
    setAnswerVote: (state, action: PayloadAction<{ answerId: string; type: VoteTypeE | null }>) => {
      const { answerId, type } = action.payload;
      state.answerVotes[answerId] = type;
    },
  },
  extraReducers: (builder) => {
    builder
      // Question vote cases
      .addCase(voteQuestion.pending, (state, action) => {
        const { questionId } = action.meta.arg;
        state.questionVoteLoading[questionId] = true;
        state.error = null;
      })
      .addCase(voteQuestion.fulfilled, (state, action) => {
        const { questionId, type } = action.payload;
        state.questionVoteLoading[questionId] = false;
        state.questionVotes[questionId] = type;
      })
      .addCase(voteQuestion.rejected, (state, action) => {
        const { questionId } = action.meta.arg;
        state.questionVoteLoading[questionId] = false;
        state.error = action.payload as string;
      })
      // Remove question vote cases
      .addCase(removeQuestionVote.pending, (state, action) => {
        const { questionId } = action.meta.arg;
        state.questionVoteLoading[questionId] = true;
        state.error = null;
      })
      .addCase(removeQuestionVote.fulfilled, (state, action) => {
        const { questionId } = action.payload;
        state.questionVoteLoading[questionId] = false;
        state.questionVotes[questionId] = null;
      })
      .addCase(removeQuestionVote.rejected, (state, action) => {
        const { questionId } = action.meta.arg;
        state.questionVoteLoading[questionId] = false;
        state.error = action.payload as string;
      })
      // Answer vote cases
      .addCase(voteAnswer.pending, (state, action) => {
        const { answerId } = action.meta.arg;
        state.answerVoteLoading[answerId] = true;
        state.error = null;
      })
      .addCase(voteAnswer.fulfilled, (state, action) => {
        const { answerId, type } = action.payload;
        state.answerVoteLoading[answerId] = false;
        state.answerVotes[answerId] = type;
      })
      .addCase(voteAnswer.rejected, (state, action) => {
        const { answerId } = action.meta.arg;
        state.answerVoteLoading[answerId] = false;
        state.error = action.payload as string;
      })
      // Remove answer vote cases
      .addCase(removeAnswerVote.pending, (state, action) => {
        const { answerId } = action.meta.arg;
        state.answerVoteLoading[answerId] = true;
        state.error = null;
      })
      .addCase(removeAnswerVote.fulfilled, (state, action) => {
        const { answerId } = action.payload;
        state.answerVoteLoading[answerId] = false;
        state.answerVotes[answerId] = null;
      })
      .addCase(removeAnswerVote.rejected, (state, action) => {
        const { answerId } = action.meta.arg;
        state.answerVoteLoading[answerId] = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setQuestionVote, setAnswerVote } = voteSlice.actions;
export default voteSlice.reducer;
