import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectAnnouncementState = (state: RootState) => state.announcement;

export const selectLocationData = createSelector(
  [selectAnnouncementState],
  (announcementState) => announcementState.data || [],
);

export const selectNearbyFilters = createSelector(
  [selectAnnouncementState],
  (announcementState) => announcementState.filters || {},
);
