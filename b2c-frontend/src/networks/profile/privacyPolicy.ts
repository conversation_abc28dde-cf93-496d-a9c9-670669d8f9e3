import { apiCall } from '@/src/services/api';
import {
  AcceptPrivacyPolicyResultI,
  FetchPrivacyPolicyBodyI,
  FetchPrivacyPolicyResultI,
} from './types';

export const acceptPrivacyPolicyAPI = async () => {
  const result = await apiCall<void, AcceptPrivacyPolicyResultI>(
    '/backend/api/v1/privacy-policy/accept',
    'POST',
    {
      isAuth: true,
    },
  );

  return result;
};

export const fetchPrivacyPolicyAPI = async (
  payload: FetchPrivacyPolicyBodyI,
): Promise<FetchPrivacyPolicyResultI> => {
  const result = await apiCall<FetchPrivacyPolicyBodyI, FetchPrivacyPolicyResultI>(
    '/backend/api/v1/privacy-policy',
    'GET',
    {
      isAuth: false,
      query: { ...payload },
    },
  );

  return result;
};
