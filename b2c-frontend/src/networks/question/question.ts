import { apiCall } from '@/src/services/api';
import type {
  CreateQuestionPayloadI,
  QuestionCreateResponseI,
  QuestionI,
  ForumQuestionFetchManyPayloadI,
  ForumQuestionFetchManyResultI,
  ForumQuestionDetailWithAnswersI,
  QuestionDeleteOnePayloadI,
} from './types';

export const createQuestionAPI = async (
  payload: CreateQuestionPayloadI,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    '/backend/api/v1/forum/question',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchQuestionAPI = async (id: string): Promise<QuestionI> => {
  const result = await apiCall<{ id: string }, QuestionI>(
    `/backend/api/v1/forum/question/${id}`,
    'GET',
    {
      isAuth: true,
      query: { id },
    },
  );

  return result;
};

export const editQuestionAPI = async (
  payload: CreateQuestionPayloadI,
  questionId: string,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    `/backend/api/v1/forum/question/${questionId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchForumQuestionsAPI = async (
  query: ForumQuestionFetchManyPayloadI,
): Promise<ForumQuestionFetchManyResultI> => {
  const result = await apiCall<unknown, ForumQuestionFetchManyResultI>(
    '/backend/api/v1/forum/questions',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const fetchForumQuestionForClientAPI = async (
  questionId: string,
): Promise<ForumQuestionDetailWithAnswersI> => {
  return apiCall<{ id: string }, ForumQuestionDetailWithAnswersI>(
    `/backend/api/v1/forum/question/${questionId}`,
    'GET',
    {
      isAuth: true,
      query: { id: questionId },
    },
  );
};

export const deleteQuestionAPI = async (payload: QuestionDeleteOnePayloadI): Promise<void> => {
  return apiCall<QuestionDeleteOnePayloadI, void>(
    `/backend/api/v1/forum/question/${payload.questionId}`,
    'DELETE',
    {
      isAuth: true,
    },
  );
};

export const updateLiveModeAPI = async (
  questionId: string,
  isLive: boolean,
): Promise<{ isLive: boolean; liveStartedAt: string | null }> => {
  return apiCall<
    { questionId: string; isLive: boolean },
    { isLive: boolean; liveStartedAt: string | null }
  >('/backend/api/v1/forum/question-live', 'PATCH', {
    isAuth: true,
    payload: { questionId, isLive },
  });
};
