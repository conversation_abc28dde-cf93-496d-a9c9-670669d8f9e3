import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI } from '@/src/types/common/data';
import { postCodePlaceDetail } from '@/src/screens/EditAnnouncement/components/EditAnnouncement/types';
import { IdTypeI } from '../question/types';

export type fetchPeopleBodyI = {
  latitude: number;
  longitude: number;
};

export type fetchPeopleResultDataI = {
  id: string;
  name: string;
  avatar: string | null;
  cursorId?: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
  distanceInMeters: number;
  nextCursorId?: string;
  latitude: number;
  longitude: number;
};

export type fetchPeopleResultI = {
  data: fetchPeopleResultDataI[];
  nextCursorId: null | string;
};

export type fetchPeopleQueryI = {
  cursorId: number | null;
  pageSize: number;
};

export type addAnnouncementBodyI = {
  title: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  description: string;
  latitude?: number;
  longitude?: number;
  addressMapBox?: postCodePlaceDetail;
  city?: IdTypeI;
  cityMapBox?: IdNameI;
  countryIso2?: string;
  postCodeMapBox?: postCodePlaceDetail;
};
