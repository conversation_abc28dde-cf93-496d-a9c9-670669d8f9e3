import { apiCall } from '@/src/services/api';
import { fetchPeopleBodyI, fetchPeopleQueryI, fetchPeopleResultI } from './types';

export const fetchPeopleNearby = async (
  query: fetchPeopleQueryI,
  payload: fetchPeopleBodyI,
): Promise<fetchPeopleResultI> => {
  const result = await apiCall<fetchPeopleBodyI, fetchPeopleResultI>(
    '/backend/api/v1/near-by',
    'POST',
    {
      isAuth: true,
      payload,
      query,
    },
  );

  return result;
};
