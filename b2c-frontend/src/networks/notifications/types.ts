export type NotificationType =
  | 'PUBLIC'
  | 'LIKE'
  | 'COMMENT'
  | 'REPLY'
  | 'FOLLOWER'
  | 'REQUEST_RECEIVED'
  | 'REQUEST_ACCEPTED'
  | 'MESSAGE'
  | 'FORUM_QUESTION_LIVE'
  | 'FORUM_QUESTION_VOTE'
  | 'FORUM_QUESTIONS'
  | 'FORUM_ANSWER'
  | 'FORUM_ANSWER_VOTE'
  | 'FORUM_QUESTION_COMMENT'
  | 'FORUM_QUESTION_REPLY'
  | 'FORUM_ANSWER_COMMENT'
  | 'FORUM_ANSWER_REPLY'
  | 'FORUM_ANSWER_VERIFIED';

export interface NotificationFetchManyItemI {
  actorProfileId: string;
  postId?: string;
  questionId?: string;
  answerId?: string;
  requestId?: string;
  messageContent?: string;
}

export type NotificationFetchManyI = NotificationFetchManyItemI[];

export interface ProfileForNotificationI {
  id: string;
  avatar: string | null;
  name: string;
}

export interface PostForNotificationI {
  id: string;
  caption: string;
  image?: string;
}

export interface NotificationFetchManyResultI {
  profiles: ProfileForNotificationI[] | null;
  posts: PostForNotificationI[] | null;
}

export interface NotificationData {
  actorProfileId?: string;
  postId?: string;
  commentId?: string;
  parentCommentId?: string;
  questionId?: string;
  answerId?: string;
  requestId?: string;
  voteType?: string;
  messageContent?: string;
  isMedia?: string;
  mediaUrl?: string;
  mediaType?: string;
  screen?: string;
  type: NotificationType;
}

export interface NotificationFetchManyQueryI {
  id: string;
  type: NotificationType;
  data: NotificationData;
  isRead: boolean;
  createdAt: string;
  content: {
    title: string;
    body: string;
    data?: NotificationData;
  };
}

export type NotificationFetchManyResult = {
  notifications: NotificationFetchManyQueryI[];
  nextCursor: string | null;
};

export interface NotificationUpdateReadI {
  profileId: string;
  type: 'UPDATE_READ';
  ids: string[];
}

export interface NotificationUpdateReadResult {
  success: boolean;
  updatedCount: number;
}
