/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  GetQuestionCommentRepliesPayloadI,
  GetQuestionCommentsPayloadI,
  GetQuestionCommentsResponseI,
  QuestionCommentCreateI,
  QuestionCommentCreateResponseI,
} from './types';

export const addForumCommentAPI = async (
  payload: QuestionCommentCreateI,
): Promise<QuestionCommentCreateResponseI> => {
  const result = await apiCall<QuestionCommentCreateI, QuestionCommentCreateResponseI>(
    '/backend/api/v1/forum/question-comment',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchForumCommentsAPI = async (
  query: GetQuestionCommentsPayloadI,
): Promise<GetQuestionCommentsResponseI> => {
  const result = await apiCall<GetQuestionCommentsPayloadI, GetQuestionCommentsResponseI>(
    '/backend/api/v1/forum/question-comments',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const fetchForumCommentRepliesAPI = async (
  query: GetQuestionCommentRepliesPayloadI,
): Promise<GetQuestionCommentsResponseI> => {
  const result = await apiCall<GetQuestionCommentRepliesPayloadI, GetQuestionCommentsResponseI>(
    '/backend/api/v1/forum/question-comment-replies',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const deleteForumCommentAPI = async (commentId: string): Promise<unknown> => {
  const result = await apiCall(`/backend/api/v1/forum/question-comment`, 'DELETE', {
    isAuth: true,
    query: { id: commentId },
  });

  return result;
};
