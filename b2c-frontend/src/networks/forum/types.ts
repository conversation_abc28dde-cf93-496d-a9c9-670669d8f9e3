/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ObjUnknownI } from '@/src/types/common/data';
import { AuthorProfileI, CommentItemI } from '../content/types';

export type QuestionCommentCreateI = {
  questionId: string;
  text: string;
  parentCommentId?: string;
};

export type QuestionCommentCreateResponseI = {
  id: string;
  cursorId: number;
};

export type GetQuestionCommentsPayloadI = ObjUnknownI & {
  questionId: string;
  cursorId: number | null;
  pageSize?: number;
};

export type GetQuestionCommentsResponseI = {
  data: QuestionCommentI[];
  total: number;
  nextCursorId: number | null;
};

export type QuestionCommentI = CommentItemI & {
  questionId: string;
  replyCount: number;
  profile?: AuthorProfileI | null;
  Profile?: AuthorProfileI | null;
  replies?: QuestionCommentReplyI[];
};

export type AnswerCommentCreateI = {
  text: string;
  answerId: string;
  parentCommentId?: string;
};

export type AnswerCommentCreateResponseI = {
  id: string;
  cursorId: number;
};

export type GetAnswerCommentsPayloadI = {
  answerId: string;
  cursorId?: number | null;
  pageSize?: number;
};

export type GetAnswerCommentsResponseI = {
  data: AnswerCommentI[];
  total: number;
  nextCursorId: number | null;
};

export type GetAnswerCommentRepliesPayloadI = {
  answerId: string;
  parentCommentId: string;
  cursorId?: number | null;
  pageSize?: number;
};

export type AnswerCommentI = {
  id: string;
  text: string;
  replyCount: number;
  createdAt: string;
  profile: {
    id: string;
    name: string;
    avatar: string;
  };
  replies: AnswerCommentReplyI[] | null;
  cursorId: number;
};

export type AnswerCommentReplyI = {
  id: string;
  text: string;
  createdAt: string;
  profile: {
    id: string;
    name: string;
    avatar: string;
  };
  cursorId: number;
};

export type QuestionCommentReplyI = CommentItemI & {};

export type GetQuestionCommentRepliesPayloadI = ObjUnknownI & {
  questionId: string;
  parentCommentId: string;
  cursorId: number | null;
  pageSize: number;
};

export type VoteTypeE = 'UPVOTE' | 'DOWNVOTE';

export type ForumQuestionVoteI = {
  id: string;
  cursorId: number;
};

export type ForumQuestionVoteFetchManyI = {
  questionId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumQuestionVoteCreateOneI = {
  questionId: string;
  type: VoteTypeE;
};

export type ForumQuestionVoteDeleteOneI = {
  questionId: string;
};

export type ProfileExternalI = {
  id: string;
  avatar: string | null;
  name: string;
  designationText: string | null;
  cursorId: number;
};

export type TotalCursorDataI<T> = {
  total: number;
  data: T[];
  nextCursorId: number | null;
};

export type ForumQuestionVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;

export type ForumAnswerVoteI = {
  id: string;
  cursorId: number;
};

export type ForumAnswerVoteFetchManyI = {
  answerId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumAnswerVoteCreateOneI = {
  answerId: string;
  type: VoteTypeE;
};

export type ForumAnswerVoteDeleteOneI = {
  answerId: string;
};

export type ForumAnswerVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;

export type GlobalSearchParamsI = {
  search: string;
  page?: number;
  pageSize?: number;
  type?: 'questions' | 'communities' | 'all';
};

export type GlobalSearchResponseI<T> = {
  data: T[];
  total: number;
};

export type GlobalSearchQuestionItemI = {
  id: string;
  title: string;
  description: string | null;
  type: string;
  communityId: string;
  communityName: string;
  departmentId: string | null;
  departmentName: string | null;
  equipmentCategoryId: string | null;
  equipmentCategoryName: string | null;
  equipmentModelId: string | null;
  equipmentModelName: string | null;
  equipmentManufacturerId: string | null;
  equipmentManufacturerName: string | null;
  answerCount: number;
  upvoteCount: number;
  createdAt: string;
  profileName: string;
  matchedFields: string[];
  topics: Array<{
    id: string;
    name: string;
    type: string;
  }>;
};

export type GlobalSearchCommunityItemI = {
  id: string;
  name: string;
  description: string | null;
  access: string;
  isRestricted: boolean;
  memberCount: number;
  questionCount: number;
  matchedFields: string[];
};

export type GlobalSearchCombinedResponseI = {
  questions?: GlobalSearchResponseI<GlobalSearchQuestionItemI>;
  communities?: GlobalSearchResponseI<GlobalSearchCommunityItemI>;
};
