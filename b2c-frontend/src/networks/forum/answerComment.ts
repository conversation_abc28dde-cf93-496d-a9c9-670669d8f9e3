/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  GetAnswerCommentRepliesPayloadI,
  GetAnswerCommentsPayloadI,
  GetAnswerCommentsResponseI,
  AnswerCommentCreateI,
  AnswerCommentCreateResponseI,
} from './types';

export const addForumAnswerCommentAPI = async (
  payload: AnswerCommentCreateI,
): Promise<AnswerCommentCreateResponseI> => {
  const result = await apiCall<AnswerCommentCreateI, AnswerCommentCreateResponseI>(
    '/backend/api/v1/forum/answer-comment',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchForumAnswerCommentsAPI = async (
  query: GetAnswerCommentsPayloadI,
): Promise<GetAnswerCommentsResponseI> => {
  const result = await apiCall<GetAnswerCommentsPayloadI, GetAnswerCommentsResponseI>(
    '/backend/api/v1/forum/answer-comments',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const fetchForumAnswerCommentRepliesAPI = async (
  query: GetAnswerCommentRepliesPayloadI,
): Promise<GetAnswerCommentsResponseI> => {
  const result = await apiCall<GetAnswerCommentRepliesPayloadI, GetAnswerCommentsResponseI>(
    '/backend/api/v1/forum/answer-comment-replies',
    'GET',
    {
      query,
      isAuth: true,
    },
  );

  return result;
};

export const deleteForumAnswerCommentAPI = async (commentId: string): Promise<unknown> => {
  const result = await apiCall(`/backend/api/v1/forum/answer-comment/${commentId}`, 'DELETE', {
    isAuth: true,
  });

  return result;
};
