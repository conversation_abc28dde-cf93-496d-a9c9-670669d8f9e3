/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { AuthRegisterBodyI, AuthRegisterResultI } from './types';

export const registerAPI = async (payload: AuthRegisterBodyI): Promise<AuthRegisterResultI> => {
  const result = await apiCall<AuthRegisterBodyI, AuthRegisterResultI>(
    '/backend/api/v1/auth/register',
    'POST',
    {
      isAuth: false,
      payload,
    },
  );

  return result;
};
