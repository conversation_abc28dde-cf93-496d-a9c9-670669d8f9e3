/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

export const strongPasswordRegex =
  /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[@$!%*?&^#])[A-Za-z0-9@$!%*?&^#]{8,32}$/;

export const IsShipImoR = /^\d{1,7}$/;

export const VisaNameR = /^[a-zA-Z0-9\s\-'.()\/]+$/;
