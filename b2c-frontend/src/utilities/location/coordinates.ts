export const formatLatitude = (lat: number): number => {
  const validatedLat = Math.max(-90, Math.min(90, lat));
  const [wholePart, decimalPart = ''] = validatedLat.toString().split('.');
  const paddedWhole = wholePart.padStart(2, '0');
  const paddedDecimal = decimalPart.padEnd(6, '0').slice(0, 6);
  return parseFloat(`${paddedWhole}.${paddedDecimal}`);
};

export const formatLongitude = (lng: number): number => {
  const validatedLng = Math.max(-180, Math.min(180, lng));
  const [wholePart, decimalPart = ''] = validatedLng.toString().split('.');
  const isNegative = wholePart.startsWith('-');
  const digitsPart = isNegative ? wholePart.slice(1) : wholePart;
  const paddedDigits = digitsPart.padStart(3, '0');
  const paddedWhole = isNegative ? `-${paddedDigits}` : paddedDigits;
  const paddedDecimal = decimalPart.padEnd(6, '0').slice(0, 6);
  return parseFloat(`${paddedWhole}.${paddedDecimal}`);
};
