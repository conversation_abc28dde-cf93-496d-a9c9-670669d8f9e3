import { useEffect, useState } from 'react';
import { Platform, Pressable, Text, View } from 'react-native';
import Modal from 'react-native-modal';
import DateTimePicker, { type DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { twMerge } from 'tailwind-merge';
import Clock from '@/src/assets/svgs/Clock';
import TextView from '../TextView';
import { TimePickerPropsI } from './types';

const TimePicker = ({
  title,
  selectedTime: initialSelectedTime,
  onTimeChange,
  disabled = false,
  className,
}: TimePickerPropsI) => {
  const [time, setTime] = useState<Date>(
    initialSelectedTime ? new Date(initialSelectedTime) : new Date(),
  );
  const [visible, setVisible] = useState<boolean>(false);
  const [formattedTime, setFormattedTime] = useState<string>('');

  useEffect(() => {
    const formatTime = time.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
    setFormattedTime(formatTime);
  }, [time]);

  const handleChange = (event: DateTimePickerEvent, newSelectedTime?: Date) => {
    if (event.type === 'set') {
      const selected =
        newSelectedTime ??
        (event?.nativeEvent?.timestamp ? new Date(event.nativeEvent.timestamp) : null);
      if (selected) {
        setTime(selected);
        onTimeChange(selected);
      }
    }

    if (Platform.OS === 'android') {
      setVisible(false);
    }
  };

  const renderTimePicker = () => {
    if (Platform.OS === 'ios') {
      return (
        <Modal
          isVisible={visible}
          onBackdropPress={() => setVisible(false)}
          onBackButtonPress={() => setVisible(false)}
          style={{ margin: 0, justifyContent: 'flex-end' }}
          animationIn="slideInUp"
          animationOut="slideOutDown"
          animationInTiming={250}
          animationOutTiming={250}
          backdropTransitionInTiming={250}
          backdropTransitionOutTiming={1}
          statusBarTranslucent
          useNativeDriverForBackdrop
          hideModalContentWhileAnimating={false}
          avoidKeyboard
        >
          <View
            style={{
              backgroundColor: 'white',
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              paddingTop: 20,
              zIndex: 1000,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                paddingHorizontal: 20,
                paddingBottom: 10,
              }}
            >
              <Pressable onPress={() => setVisible(false)}>
                <Text style={{ color: '#448600', fontSize: 16 }}>Cancel</Text>
              </Pressable>
              <Pressable
                onPress={() => {
                  onTimeChange(time);
                  setVisible(false);
                }}
              >
                <Text style={{ color: '#448600', fontSize: 16, fontWeight: 'bold' }}>Done</Text>
              </Pressable>
            </View>
            <View className="items-center">
              <DateTimePicker
                value={time}
                mode="time"
                display="spinner"
                onChange={handleChange}
                style={{ height: 200 }}
                is24Hour={false}
              />
            </View>
          </View>
        </Modal>
      );
    }

    return visible ? (
      <DateTimePicker
        value={time}
        mode="time"
        display="default"
        onChange={handleChange}
        is24Hour={false}
      />
    ) : null;
  };

  return (
    <View className="">
      <Text className="pb-2 text-black font-medium">{title}</Text>
      <Pressable onPress={() => !disabled && setVisible(true)} disabled={disabled}>
        <View
          className={twMerge(
            `border border-[#D4D4D4] flex-row items-center rounded-xl py-2 w-full justify-center ${
              disabled ? 'bg-gray-100' : ''
            }`,
            className,
          )}
        >
          <TextView
            subtitle={formattedTime || 'HH:MM'}
            subtitleClassName={`pb-3 pl-3 font-inter text-[13px] ${
              !initialSelectedTime ? 'text-gray-400' : 'text-gray-700'
            }`}
          />
          <View className="ml-auto pr-[14px]">
            <Clock />
          </View>
        </View>
      </Pressable>
      {renderTimePicker()}
    </View>
  );
};

export default TimePicker;
