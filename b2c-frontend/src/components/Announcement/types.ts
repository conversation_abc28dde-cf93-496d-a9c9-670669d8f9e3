export type AnnouncementItemI = {
  id: string;
  cursorId: string;
  creatorId: string;
  title: string;
  description: string;
  latitude?: number;
  longitude?: number;
  addressRawDataId: string | null;
  cityId: string | null;
  cityRawDataId: string | null;
  countryIso2: string | null;
  startDate: Date;
  endDate: Date;
  startTime: string;
  endTime: string;
  totalAttendees: number;
  createdAt: Date;
  updatedAt: Date;
  isAttending: boolean;
};

export type ProfileItemI = {
  id: string;
  name: string;
  avatar: string | null;
  designationText: string | null;
  entityText: string | null;
  designationAlternativeId: string | null;
  designationRawDataId: string | null;
  entityId: string | null;
  entityRawDataId: string | null;
};

export interface AnnouncementProps {
  announcement: AnnouncementItemI;
  user: ProfileItemI;
}
