import { useState } from 'react';
import { Text, View } from 'react-native';
import Clock from '@/src/assets/svgs/Clock';
import Location from '@/src/assets/svgs/Location';
import Tick from '@/src/assets/svgs/Tick';
import Button from '../Button';
import StackedAvatar from '../StackedAvatar';
import UserAvatar from '../UserAvatar';
import { AnnouncementProps } from './types';

export const Announcement = ({ announcement, user }: AnnouncementProps) => {
  const [isJoining, setIsJoining] = useState(announcement.isAttending || false);

  const handleJoin = () => {
    setIsJoining(!isJoining);
  };

  const formatTime = (time: string) => {
    return new Date(`1970-01-01T${time}`).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <View className="pt-4">
      <View className="flex-row justify-between items-center">
        <View className="flex-row items-center gap-3">
          <UserAvatar avatarUri={user.avatar} name={user.name} />
          <View>
            <Text className="font-medium text-base text-gray-900">{user.name}</Text>
            <Text className="text-gray-500 text-sm">
              {user.designationText} {user.entityText && `at ${user.entityText}`}
            </Text>
            <Text className="text-gray-500 text-sm">{formatDate(announcement.createdAt)}</Text>
          </View>
        </View>
      </View>

      <Text className="text-base text-gray-800 w-full whitespace-normal pb-4">
        {announcement.description}
      </Text>

      <View className="border border-borderGrayLight p-4 rounded-lg flex gap-1">
        <Text className="text-lg font-medium">{announcement.title}</Text>
        <View className="flex-row gap-2 items-center">
          <StackedAvatar imageUrls={[]} showCount={false} />
          <Text className="text-sm leading-4">{announcement.totalAttendees} attending</Text>
        </View>
        <View className="flex gap-1 my-4">
          {announcement.latitude && announcement.longitude && (
            <View className="flex-row items-center gap-1">
              <Location width={16} height={16} />
              <Text>Location</Text>
            </View>
          )}
          <View className="flex-row flex-wrap items-start gap-1">
            <Clock width={16} height={16} />
            <Text className="flex-1">
              {formatTime(announcement.startTime)} - {formatTime(announcement.endTime)}
            </Text>
          </View>
          <Text className="text-sm text-gray-600">
            {formatDate(announcement.startDate)} - {formatDate(announcement.endDate)}
          </Text>
        </View>
        <Button
          variant={isJoining ? 'primaryOutline' : 'outline'}
          className="rounded-lg mb-1"
          label={isJoining ? 'I am attending' : 'Join now'}
          prefixIcon={isJoining ? <Tick /> : undefined}
          onPress={handleJoin}
        />
      </View>
    </View>
  );
};
