import { Text, TouchableOpacity, View } from 'react-native';
import { ToggleSwitchPropsI } from './types';

const ToggleSwitch = ({ enabled, onToggle, label }: ToggleSwitchPropsI) => (
  <View className="flex-row items-center justify-between gap-2">
    <TouchableOpacity
      onPress={onToggle}
      className={`w-10 h-6 rounded-full flex-row items-center px-1 ${
        enabled ? 'bg-primaryGreen justify-end' : 'bg-toggleGray justify-start'
      }`}
    >
      <View className="w-4 h-4 rounded-full bg-white shadow-sm" />
    </TouchableOpacity>
    <Text className="text-base text-gray-600 flex-1 mr-4">{label}</Text>
  </View>
);

export default ToggleSwitch;
