/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Image, Text, View } from 'react-native';

const StackedAvatar = ({
  imageUrls = [],
  size = 25,
  overlap = 15,
  maxDisplayed = 5,
  showCount = true,
}: {
  imageUrls: string[];
  size?: number;
  overlap?: number;
  maxDisplayed?: number;
  showCount?: boolean;
}) => {
  if (!imageUrls || imageUrls.length === 0) return null;

  const displayCount = Math.min(imageUrls.length, maxDisplayed);
  const remainingCount = imageUrls.length - displayCount;
  const displayedImages = imageUrls.slice(0, displayCount);
  const showRemainingCount = showCount && remainingCount > 0;

  const containerWidth =
    displayCount * (size - overlap) + overlap + (showRemainingCount ? size - overlap : 0);

  const getLeftPosition = (index: number) => {
    return index * (size - overlap);
  };

  const getZIndex = (index: number) => {
    return 50 - index;
  };

  return (
    <View className="relative" style={{ width: containerWidth, height: size }}>
      {displayedImages.map((url, index) => (
        <View
          key={index}
          className={`absolute rounded-full  overflow-hidden bg-gray-200`}
          style={{
            left: getLeftPosition(index),
            zIndex: getZIndex(index),
            width: size,
            height: size,
            borderRadius: size / 2,
          }}
        >
          <Image source={{ uri: url }} className="w-full h-full" resizeMode="cover" />
        </View>
      ))}

      {showRemainingCount && remainingCount > 0 && (
        <View
          className="absolute rounded-full border-2 border-white overflow-hidden bg-gray-500"
          style={{
            left: getLeftPosition(displayCount),
            zIndex: getZIndex(displayCount),
            width: size,
            height: size,
            borderRadius: size / 2,
          }}
        >
          <Image
            source={{ uri: imageUrls[displayCount] }}
            className="w-full h-full opacity-50"
            resizeMode="cover"
          />
          <View className="absolute inset-0 bg-black bg-opacity-40 items-center justify-center">
            <Text className="text-white font-bold" style={{ fontSize: size * 0.4 }}>
              +{remainingCount}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default StackedAvatar;
