import { useState } from 'react';
import { Modal, Pressable, Text, View } from 'react-native';
import PrivacyModal from '@/src/components/PrivacyModal';
import TermsModal from '@/src/components/TermsModal';
import Information from '@/src/assets/svgs/Information';
import type { InfoModalProps } from './types';

const InfoTexts = {
  country:
    'Your work profile and country help us personalize your experience — connecting you with relevant peers, discussions, and content across the platform.',
  workDetails:
    'Your work profile and country help us personalize your experience — connecting you with relevant peers, discussions, and content across the platform.',
};

const InfoModal = ({ type }: InfoModalProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  const handlePrivacyPress = () => {
    setIsVisible(false);
    setShowPrivacyModal(true);
  };

  const handleTermsPress = () => {
    setIsVisible(false);
    setShowTermsModal(true);
  };

  return (
    <>
      <Pressable className="flex-row items-center gap-1" onPress={() => setIsVisible(!isVisible)}>
        <Information width={2} height={2} />
        <Text className="text-small text-subLabelGray ml-1">Why is this information useful?</Text>
      </Pressable>

      <Modal
        visible={isVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsVisible(false)}
      >
        <View className="flex-1 justify-end bg-black/40">
          <Pressable className="flex-1" onPress={() => setIsVisible(false)} />
          <View className="bg-white rounded-t-3xl shadow-2xl">
            <View className="w-12 h-1 bg-gray-300 rounded-full self-center mt-3 mb-6" />

            <View className="px-6 pb-8">
              <Text className="text-xl font-semibold text-gray-900 mb-1">Why we ask for this</Text>

              <Text className="text-base text-gray-600 leading-6 mb-8">
                {InfoTexts[type as keyof typeof InfoTexts]}
              </Text>

              <View className="space-y-4 mb-8">
                <Pressable className="flex-row items-center py-3" onPress={handlePrivacyPress}>
                  <Text className="text-[#004687] text-base font-medium">Privacy Policy</Text>
                </Pressable>

                <View className="h-px bg-gray-200" />

                <Pressable className="flex-row items-center py-3" onPress={handleTermsPress}>
                  <Text className="text-[#004687] text-base font-medium">Terms & Conditions</Text>
                </Pressable>
              </View>

              <Pressable
                className="bg-primaryGreen py-4 px-6 rounded-xl"
                onPress={() => setIsVisible(false)}
              >
                <Text className="text-white font-semibold text-center text-base">Got it</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>

      <PrivacyModal isVisible={showPrivacyModal} onClose={() => setShowPrivacyModal(false)} />
      <TermsModal isVisible={showTermsModal} onClose={() => setShowTermsModal(false)} />
    </>
  );
};

export default InfoModal;
