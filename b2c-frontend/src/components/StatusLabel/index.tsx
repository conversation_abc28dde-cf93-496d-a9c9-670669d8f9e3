/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, View } from 'react-native';
import Approve from '@/src/assets/svgs/Approve';
import Pending from '@/src/assets/svgs/Pending';
import Reject from '@/src/assets/svgs/Reject';
import { StatusLabelPropsI, StatusVariantI } from './types';

const variantConfig: Record<StatusVariantI, { color: string; Icon: React.ComponentType }> = {
  rejected: { color: 'text-rejectionRed', Icon: Reject },
  approved: { color: 'text-successGreen', Icon: Approve },
  pending: { color: 'text-warningYellow', Icon: Pending },
};

export const StatusLabel = ({ label, variant }: StatusLabelPropsI) => {
  if (!variant) return;
  const { color, Icon } = variantConfig[variant];

  return (
    <View className="flex-row items-center gap-1">
      <Text className={`text-sm font-medium ${color}`}>{label}</Text>
      <Icon />
    </View>
  );
};
