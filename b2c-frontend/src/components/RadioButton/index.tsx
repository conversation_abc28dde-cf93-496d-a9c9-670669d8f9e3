import { Text, TouchableOpacity, View } from 'react-native';
import { RadioButtonPropsI } from './types';

const RadioButton = ({
  selected,
  onPress,
  label,
  description,
  children,
  size = 6,
  inFilter = false,
}: RadioButtonPropsI) => (
  <TouchableOpacity onPress={onPress}>
    <View className="flex-row items-start">
      <View className="mt-1 mr-4">
        <View
          className={`w-${size} h-${size} rounded-full border-2 border-gray-400 flex items-center justify-center`}
        >
          {selected && (
            <View className={`w-${size - 3} h-${size - 3} rounded-full bg-primaryGreen`} />
          )}
        </View>
      </View>
      <View className="flex-1">
        {!inFilter && <Text className="text-lg font-medium text-gray-900 mb-2">{label}</Text>}
        {inFilter && <Text className="text-base font-normal text-gray-900 mb-2">{label}</Text>}
        {description ? <Text className="text-base text-gray-600 mb-4">{description}</Text> : <></>}
        {children}
      </View>
    </View>
  </TouchableOpacity>
);
export default RadioButton;
