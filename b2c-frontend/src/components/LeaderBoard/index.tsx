/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { View, Text, Pressable, FlatList, RefreshControl, ActivityIndicator } from 'react-native';
import BackButton from '../BackButton';
import Tabs from '../Tabs';
import UserAvatar from '../UserAvatar';
import { LeaderBoardDurationI, LeaderBoardItemI, LeaderBoardPropsI } from './types';



const LeaderBoardItem = ({ item, index }: { item: LeaderBoardItemI; index: number }) => {
  const profile = item.Profile;
  const name = profile.name || 'Anonymous User';
  const avatar = profile.avatar;
  const title = profile.designation?.name;
  const company = profile.entity?.name;
  const rank = index + 1;

  return (
    <View className="py-4 px-5 border-b border-gray-100">
      <View className="flex-row items-center">
        {/* Rank Number */}
        <View className="w-8 items-center justify-center mr-3">
          <Text className="text-base font-medium text-gray-600">
            {rank}
          </Text>
        </View>

        <UserAvatar avatarUri={avatar} name={name} />
        <View className="ml-4 flex-1">
          <Text className="text-lg font-semibold text-gray-900" numberOfLines={1}>
            {name}
          </Text>
          {title && (
            <Text className="text-sm text-gray-500" numberOfLines={1} ellipsizeMode="tail">
              {title}
              {title.toLowerCase() !== 'aspirant' && company ? ` at ${company}` : ''}
            </Text>
          )}
        </View>

        {/* Score */}
        <View className="items-center justify-center">
          <Text className="text-lg font-semibold text-gray-900">
            {item.score}
          </Text>
        </View>
      </View>
    </View>
  );
};

const LeaderBoard = ({ onBack, data, loading, refreshing, onRefresh }: LeaderBoardPropsI) => {
  const [duration, setDuration] = useState<LeaderBoardDurationI>('WEEKLY');
  const [activeTab, setActiveTab] = useState<string>('TROUBLESHOOT');

  const tabs = [
    { id: 'TROUBLESHOOT', label: 'Troubleshoot' },
    { id: 'QNA', label: 'QnA' },
    { id: 'CONTRIBUTOR', label: 'Contributor' },
  ];

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  };

  const renderEmptyComponent = () => {
    if (loading) return null;

    return (
      <View className="flex-1 justify-center items-center py-10">
        <Text className="text-gray-500 text-base">No leaderboard data available</Text>
      </View>
    );
  };

  return (
    <View className="flex-1 bg-white">
      <View className="px-4 pt-2">
        <View className="flex-row items-center justify-between mb-6">
          <BackButton
            onBack={onBack}
            label="Leaderboard"
            labelClassname="text-xl font-semibold text-gray-900"
          />
          <View className="flex-row items-center bg-gray-100 rounded-full p-1">
            <Pressable
              className={`px-4 py-2 rounded-full ${duration === 'WEEKLY' ? 'bg-primaryGreen' : 'bg-transparent'}`}
              onPress={() => setDuration('WEEKLY')}
            >
              <Text className={`text-sm font-medium ${duration === 'WEEKLY' ? 'text-white' : 'text-gray-600'}`}>
                Weekly
              </Text>
            </Pressable>
            <Pressable
              className={`px-4 py-2 rounded-full ${duration === 'OVERALL' ? 'bg-primaryGreen' : 'bg-transparent'}`}
              onPress={() => setDuration('OVERALL')}
            >
              <Text className={`text-sm font-medium ${duration === 'OVERALL' ? 'text-white' : 'text-gray-600'}`}>
                Overall
              </Text>
            </Pressable>
          </View>
        </View>

        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </View>

      <View className="flex-1 mt-4">
        <FlatList
          data={data}
          renderItem={({ item, index }) => (
            <LeaderBoardItem item={item} index={index} />
          )}
          keyExtractor={(item, index) => item.Profile?.id || index.toString()}
          refreshControl={
            onRefresh ? (
              <RefreshControl enabled={true} refreshing={refreshing || false} onRefresh={onRefresh} />
            ) : undefined
          }
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyComponent}
          contentContainerStyle={{
            flexGrow: 1,
            backgroundColor: 'white',
          }}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default LeaderBoard;