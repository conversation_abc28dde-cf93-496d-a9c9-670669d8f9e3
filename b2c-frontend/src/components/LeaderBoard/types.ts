import { ListItem } from "../UsersList/types";

export type LeaderBoardDurationI = 'WEEKLY' | 'OVERALL';
export type LeaderBoardTypeI = 'MENTOR' | 'GUIDE' | 'TROUBLESHOOT' | 'CONTRIBUTOR';

export type LeaderBoardItemI = ListItem & {
  score: number;
  rank: number;
};

export type LeaderBoardPropsI = {
  onBack: () => void;
  data: LeaderBoardItemI[];
  loading?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
};