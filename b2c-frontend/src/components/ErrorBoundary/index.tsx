import React, { Component } from 'react';
import { View, Text, Pressable, ScrollView, Share, Alert, Linking, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '../SafeArea';
import type { ErrorBoundaryProps, ErrorBoundaryState, ErrorActionProps, ErrorInfo } from './types';

const ErrorAction: React.FC<ErrorActionProps> = ({
  icon,
  label,
  onPress,
  primary = false,
  disabled = false,
}) => (
  <Pressable
    onPress={onPress}
    disabled={disabled}
    className={`
      flex-row items-center justify-center px-4 py-3 rounded-xl
      ${primary ? 'bg-green-600 active:bg-green-700' : 'bg-gray-100 active:bg-gray-200'}
      ${disabled ? 'opacity-50' : ''}
    `}
  >
    <View className="mr-2">{icon}</View>
    <Text className={`font-medium ${primary ? 'text-white' : 'text-gray-800'}`}>{label}</Text>
  </Pressable>
);

const ErrorBoundaryContent: React.FC<{
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRetrying: boolean;
  title: string;
  subtitle: string;
  onRetry: () => void;
  onReportError: () => void;
  onContactSupport: () => void;
  onBack?: () => void;
}> = ({
  error,
  errorInfo,
  isRetrying,
  title,
  subtitle,
  onRetry,
  onReportError,
  onContactSupport,
  onBack,
}) => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();

    if (onBack) {
      onBack();
    } else if (navigation.canGoBack()) {
      navigation.goBack();
    }
  };

  return (
    <SafeArea>
      <View className="px-4">
        <BackButton onBack={handleBack} label="" />
      </View>

      <ScrollView
        className="flex-1"
        contentContainerClassName="flex-grow p-6"
        showsVerticalScrollIndicator={false}
      >
        <View className="items-center justify-center mt-20 mb-8">
          <View className="w-24 h-24 rounded-full bg-red-50 items-center justify-center mb-6">
            <Text className="text-5xl">⚠️</Text>
          </View>
          <Text className="text-2xl font-bold text-gray-900 text-center mb-3">{title}</Text>
          <Text className="text-base text-gray-600 text-center px-4">{subtitle}</Text>
        </View>

        <View className="gap-y-4 mb-8">
          <ErrorAction
            icon={<Text>🔄</Text>}
            label={isRetrying ? 'Retrying...' : 'Try Again'}
            onPress={onRetry}
            primary
            disabled={isRetrying}
          />

          <ErrorAction icon={<Text>📤</Text>} label="Report Issue" onPress={onReportError} />
        </View>

        <View className="bg-green-50 rounded-xl p-4 mb-6">
          <Text className="text-green-800 font-medium mb-2">Quick fixes:</Text>
          <View className="space-y-1">
            <Text className="text-green-700 text-sm">• Try refreshing the app</Text>
            <Text className="text-green-700 text-sm">• Check your internet connection</Text>
            <Text className="text-green-700 text-sm">• Restart the app if needed</Text>
          </View>
        </View>

        <Pressable onPress={onContactSupport} className="items-center py-4">
          <Text className="text-green-600 font-medium">Contact Support</Text>
        </Pressable>
      </ScrollView>
    </SafeArea>
  );
};

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  private lastAction: (() => void) | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRetrying: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo: {
        componentStack: errorInfo.componentStack!,
      },
    });

    if (this.props.onError) {
      this.props.onError(error, {
        componentStack: errorInfo.componentStack!,
      });
    }

    if (__DEV__) {
      console.error('Error caught by ErrorBoundary:', error);
      console.error('Component stack:', errorInfo.componentStack);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  setLastAction = (action: () => void) => {
    this.lastAction = action;
  };

  handleRetry = () => {
    this.setState({ isRetrying: true });

    this.retryTimeoutId = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRetrying: false,
      });

      if (this.lastAction) {
        try {
          this.lastAction();
        } catch (error) {
          console.error('Error during retry action:', error);
        }
      }
    }, 1000);
  };

  handleReportError = async () => {
    const { error } = this.state;
    if (!error) return;

    const errorSummary = `
App Error Report
Error: ${error.message}
Stack: ${error.stack || 'No stack trace available'}
Platform: ${Platform.OS} ${Platform.Version}
Timestamp: ${new Date().toISOString()}
    `.trim();

    try {
      await Share.share({
        message: errorSummary,
        title: 'App Error Report',
      });
    } catch (err) {
      Alert.alert('Error', 'Failed to share error details');
    }
  };

  handleContactSupport = () => {
    const { error } = this.state;
    const subject = encodeURIComponent('App Error Report');
    const body = encodeURIComponent(`
Error: ${error?.message || 'Unknown error'}
Platform: ${Platform.OS} ${Platform.Version}
Timestamp: ${new Date().toISOString()}

Please describe what you were doing when this error occurred:
    `);

    Linking.openURL(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    const { hasError, isRetrying } = this.state;
    const {
      children,
      fallback,
      title = 'Something went wrong',
      subtitle = "We're working to fix this issue. Please try again.",
      onBack,
    } = this.props;

    if (!hasError) {
      if (React.isValidElement(children)) {
        return React.cloneElement(children as React.ReactElement<any>, {
          setErrorBoundaryAction: this.setLastAction,
        });
      }
      return children;
    }

    if (fallback) {
      return fallback(this.state.error!, this.state.errorInfo!, this.handleRetry);
    }

    return (
      <ErrorBoundaryContent
        error={this.state.error}
        errorInfo={this.state.errorInfo}
        isRetrying={isRetrying}
        title={title}
        subtitle={subtitle}
        onRetry={this.handleRetry}
        onReportError={this.handleReportError}
        onContactSupport={this.handleContactSupport}
        onBack={onBack}
      />
    );
  }
}

export default ErrorBoundary;
