/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type OTPInputPropsI = {
  title: string;
  subtitle: string;
  value: string;
  onChange: (value: string) => void;
  length?: number;
  autoFocus?: boolean;
};

export type OTPInputHandle = {
  setInvalidOTP: () => void;
  clear: () => void;
};
