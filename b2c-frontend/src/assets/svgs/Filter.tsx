/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const Filter: React.FC<OutlinedIconPropsI> = ({
  width = 3.5,
  height = 3.4,
  stroke = '#000000',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Filter',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 19"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M19.62 1.643A1.482 1.482 0 0018.25.75h-16.5A1.5 1.5 0 00.644 3.259l.007.008 6.35 6.78v7.203a1.5 1.5 0 002.332 1.249l3-2a1.498 1.498 0 00.668-1.25v-5.202l6.35-6.78.008-.008a1.482 1.482 0 00.26-1.616zm-7.915 7.599a.75.75 0 00-.204.508v5.5l-3 2v-7.5a.75.75 0 00-.203-.513L1.751 2.25h16.5l-6.546 6.992z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default Filter;
