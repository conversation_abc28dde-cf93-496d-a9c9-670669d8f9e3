import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { G, Circle, Path, Defs, ClipPath } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Information: React.FC<FilledIconPropsI> = ({
  width = 24,
  height = 24,
  color = '#ffffff',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 40 40"
      fill={color}
      {...props}
    >
      <G clipPath="url(#clip0_13_18)">
        <Circle cx={20} cy={20} r={19.6} stroke="#000" strokeWidth={0.8} />
        <Path
          d="M19.443 31V13.546h1.375V31h-1.375zm.693-20.546a1.13 1.13 0 01-.795-.318c-.22-.212-.33-.47-.33-.772 0-.303.11-.561.33-.773.22-.212.485-.318.795-.318.303 0 .565.106.785.318.*************.34.773 0 .303-.11.56-.33.772-.219.213-.484.319-.795.319z"
          fill="#000"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_13_18">
          <Path fill="#fff" d="M0 0H40V40H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default Information;
