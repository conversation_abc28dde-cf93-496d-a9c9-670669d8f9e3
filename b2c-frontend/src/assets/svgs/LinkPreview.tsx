/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const LinkPreview: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Link Preview',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 22 18"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M21.5 5.272A5.102 5.102 0 0120 8.74L16.743 12a5.088 5.088 0 01-3.622 1.5h-.005a5.122 5.122 0 01-5.115-5.265.75.75 0 011.5.042 3.62 3.62 0 006.177 2.663l3.258-3.258a3.621 3.621 0 00-5.122-5.122l-1.031 1.032a.75.75 0 01-1.06-1.06l1.03-1.032a5.122 5.122 0 018.385 1.732c.258.648.381 1.342.362 2.04zM9.22 14.406l-1.032 1.03A3.6 3.6 0 015.619 16.5a3.622 3.622 0 01-2.558-6.182L6.313 7.06a3.622 3.622 0 016.188 2.663.75.75 0 001.5.042A5.142 5.142 0 0012.5 6a5.123 5.123 0 00-7.245 0L2.001 9.258A5.12 5.12 0 009.238 16.5l1.031-1.031a.75.75 0 00-1.05-1.063z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default LinkPreview;
