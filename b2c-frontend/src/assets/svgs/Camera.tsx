/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import type { OutlinedIconPropsI } from './types';

const Camera: React.FC<OutlinedIconPropsI> = ({
  width = 2.6,
  height = 2.48,
  stroke = '#A3A3A3',
  color,
  strokeWidth = 2,
  disabled,
  accessibilityLabel = 'Camera',
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 21 20"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M10.5 13.333a2.5 2.5 0 100-5 2.5 2.5 0 000 5z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M17.167 15.833h-13.334a1.667 1.667 0 01-1.666-1.666V6.667a1.667 1.667 0 011.666-1.667h3.334l1.666-2.5h3.334l1.666 2.5h3.334a1.667 1.667 0 011.666 1.667v7.5a1.667 1.667 0 01-1.666 1.666z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default Camera;
