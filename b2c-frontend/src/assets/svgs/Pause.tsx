import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { OutlinedIconPropsI } from './types';

const PauseIcon: React.FC<OutlinedIconPropsI> = ({
  width = 2,
  height = 2,
  color = '#666666',
  stroke,
  containerStyle,
  accessibilityLabel = 'Pause',
  active = false,
  disabled = false,
  ...props
}) => {
  const iconColor = stroke || color;
  const actualColor = disabled ? '#999999' : active ? '#448600' : iconColor;
  width = RFPercentage(width);
  height = RFPercentage(height);
  return (
    <View
      style={[
        {
          flexDirection: 'row',
          width,
          height,
          justifyContent: 'center',
          alignItems: 'center',
        },
        containerStyle,
      ]}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <View
        style={{
          width: width * 0.25,
          height: height * 0.7,
          backgroundColor: actualColor,
          marginRight: width * 0.1,
        }}
      />
      <View
        style={{
          width: width * 0.25,
          height: height * 0.7,
          backgroundColor: actualColor,
        }}
      />
    </View>
  );
};

export default PauseIcon;
