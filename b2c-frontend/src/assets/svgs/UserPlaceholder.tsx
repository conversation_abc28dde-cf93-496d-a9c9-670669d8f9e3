/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import Svg, { Path } from 'react-native-svg';

const UserPlaceholder = ({ width = 50, height = 50 }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 50 50" fill="none">
      <Path
        d="M25 0.000976562C11.1943 0.000976562 0 11.1931 0 24.9999C0 38.8067 11.1932 49.9988 25 49.9988C38.8079 49.9988 50 38.8067 50 24.9999C50 11.1931 38.8079 0.000976562 25 0.000976562ZM25 7.47594C29.5682 7.47594 33.27 11.1788 33.27 15.7449C33.27 20.312 29.5682 24.0138 25 24.0138C20.434 24.0138 16.7322 20.312 16.7322 15.7449C16.7322 11.1788 20.434 7.47594 25 7.47594ZM24.9945 43.4627C20.4384 43.4627 16.2655 41.8035 13.0469 39.057C12.2628 38.3883 11.8104 37.4076 11.8104 36.3787C11.8104 31.7479 15.5583 28.0417 20.1902 28.0417H29.812C34.445 28.0417 38.1786 31.7479 38.1786 36.3787C38.1786 37.4087 37.7284 38.3872 36.9432 39.0559C33.7257 41.8035 29.5517 43.4627 24.9945 43.4627Z"
        fill="black"
      />
    </Svg>
  );
};

export default UserPlaceholder;
