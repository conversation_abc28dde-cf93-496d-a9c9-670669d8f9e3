/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const PathArrow: React.FC<OutlinedIconPropsI> = ({
  width = 1.2,
  height = 1.6,
  stroke = '#2A2929',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Path Arrow',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 6 8"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path d="M6 4L0 7.031V.97L6 4z" fill={strokeColor} />
    </Svg>
  );
};

export default PathArrow;
