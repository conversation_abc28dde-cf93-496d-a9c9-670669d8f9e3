import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { OutlinedIconPropsI } from './types';

const PlayIcon: React.FC<OutlinedIconPropsI> = ({
  width = 2,
  height = 2,
  color = '#666666',
  stroke,
  containerStyle,
  accessibilityLabel = 'Play',
  active = false,
  disabled = false,
  ...props
}) => {
  const iconColor = stroke || color;
  const actualColor = disabled ? '#999999' : active ? '#448600' : iconColor;
  width = RFPercentage(width);
  height = RFPercentage(height);
  return (
    <View
      style={[
        {
          width,
          height,
          justifyContent: 'center',
          alignItems: 'center',
        },
        containerStyle,
      ]}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <View
        style={{
          width: 0,
          height: 0,
          borderLeftWidth: width * 0.5,
          borderTopWidth: height * 0.3,
          borderBottomWidth: height * 0.3,
          borderLeftColor: actualColor,
          borderTopColor: 'transparent',
          borderBottomColor: 'transparent',
        }}
      />
    </View>
  );
};

export default PlayIcon;
