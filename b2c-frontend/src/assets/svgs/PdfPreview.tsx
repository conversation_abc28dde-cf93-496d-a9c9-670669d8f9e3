/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const PdfPreview: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Pdf Preview',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 18 19"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M18 12.25a.75.75 0 01-.75.75H15v1.5h1.5a.75.75 0 110 1.5H15v1.5a.75.75 0 11-1.5 0v-5.25a.75.75 0 01.75-.75h3a.75.75 0 01.75.75zM5.625 14.125A2.625 2.625 0 013 16.75h-.75v.75a.75.75 0 11-1.5 0v-5.25a.75.75 0 01.75-.75H3a2.625 2.625 0 012.625 2.625zm-1.5 0A1.125 1.125 0 003 13h-.75v2.25H3a1.125 1.125 0 001.125-1.125zm8.25.75A3.375 3.375 0 019 18.25H7.5a.75.75 0 01-.75-.75v-5.25a.75.75 0 01.75-.75H9a3.375 3.375 0 013.375 3.375zm-1.5 0A1.875 1.875 0 009 13h-.75v3.75H9a1.875 1.875 0 001.875-1.875zM.75 8.5V1.75a1.5 1.5 0 011.5-1.5h9a.75.75 0 01.53.22l5.25 5.25a.748.748 0 01.22.53V8.5a.75.75 0 11-1.5 0V7h-4.5a.75.75 0 01-.75-.75v-4.5H2.25V8.5a.75.75 0 01-1.5 0zM12 5.5h2.69L12 2.81V5.5z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default PdfPreview;
