import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { G, Path, Defs, ClipPath } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const ProfileShip: React.FC<FilledIconPropsI> = ({
  width = 24,
  height = 24,
  color = '#000000',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 40 40"
      fill={color}
      {...props}
    >
      <G clipPath="url(#clip0_13_16)">
        <Path
          d="M20 0C8.96 0 0 8.96 0 20s8.96 20 20 20 20-8.96 20-20S31.04 0 20 0zm0 .833C30.59.833 39.167 9.41 39.167 20c0 10.59-8.577 19.167-19.167 19.167C9.41 39.167.833 30.59.833 20 .833 9.41 9.41.833 20 .833zm-1.25 6.64a.417.417 0 00-.417.416v2.085H16.25a.417.417 0 00-.417.417v2.083H13.75a.417.417 0 00-.417.417v3.897l.002.025-4.728 1.73a.417.417 0 00-.25.528l4.205 12.314a.416.416 0 00.394.282h5.847a.5.5 0 00.03 0h7.391a.417.417 0 00.393-.282l4.194-12.215a.417.417 0 00-.248-.526l-4.73-1.774v-3.978a.417.417 0 00-.416-.417h-2.084v-2.083a.417.417 0 00-.416-.417h-2.084V7.892a.417.417 0 00-.416-.417H18.75v-.002zm.417.834H20v1.617l-.833-.02V8.307zm-2.5 2.5H22.5v1.667h-5.833v-1.667zm-2.5 2.5H25v3.25l-5.26-1.974a.418.418 0 00-.285-.008h-.015a.449.449 0 00-.025.01l-5.248 1.924v-3.202zm5 2.263v15.265h-5.914L9.276 19.187l9.89-3.617zm.833 0l9.892 3.712-3.967 11.551H20V15.57z"
          fill="#000"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_13_16">
          <Path fill="#fff" d="M0 0H40V40H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default ProfileShip;
