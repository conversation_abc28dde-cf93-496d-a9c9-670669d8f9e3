/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const ExcelPreview: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Excel Preview',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 18 19"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M11.624 17.5a.75.75 0 01-.75.75H8.249a.75.75 0 01-.75-.75v-5.25a.75.75 0 111.5 0v4.5h1.875a.75.75 0 01.75.75zm-5.94-5.86a.749.749 0 00-1.045.174l-1.265 1.771-1.265-1.77a.75.75 0 10-1.218.87l1.561 2.19-1.563 2.19a.75.75 0 001.218.87l1.267-1.77 1.265 1.77a.75.75 0 001.218-.87l-1.561-2.19 1.563-2.19a.75.75 0 00-.174-1.045zm9.277 2.42c-.375-.108-.764-.22-.98-.36-.117-.077-.115-.093-.105-.178a.426.426 0 01.187-.344c.432-.292 1.438-.161 1.858-.052a.75.75 0 10.382-1.452c-.198-.051-1.969-.489-3.078.26a1.93 1.93 0 00-.839 1.4c-.187 1.49 1.28 1.914 2.156 2.167 1.131.327 1.23.462 1.199.712-.03.226-.119.312-.202.368-.431.287-1.421.146-1.832.033a.751.751 0 10-.402 1.448c.465.123.943.186 1.424.188.546 0 1.153-.094 1.64-.418a1.952 1.952 0 00.86-1.428c.207-1.623-1.341-2.076-2.268-2.344zM.749 8.5V1.75a1.5 1.5 0 011.5-1.5h9a.75.75 0 01.53.22l5.25 5.25a.748.748 0 01.22.53V8.5a.75.75 0 11-1.5 0V7h-4.5a.75.75 0 01-.75-.75v-4.5h-8.25V8.5a.75.75 0 01-1.5 0zm11.25-3h2.689l-2.689-2.69V5.5z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default ExcelPreview;
